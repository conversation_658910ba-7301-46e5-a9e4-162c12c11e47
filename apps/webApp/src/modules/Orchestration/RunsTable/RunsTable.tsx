import { BsCalendarDate } from "@react-icons/all-files/bs/BsCalendarDate";
import { IoIosArrowRoundUp } from "@react-icons/all-files/io/IoIosArrowRoundUp";
import { VscError } from "@react-icons/all-files/vsc/VscError";
import ChevronRightDouble from "@untitled-ui/icons-react/build/esm/ChevronRightDouble";
import { determineIfIsFetcherError, Orchestration } from "cargo-api";
import { Badge } from "cargo-components/Badge";
import { Button } from "cargo-components/Button";
import { Empty } from "cargo-components/Empty";
import { LabelBox } from "cargo-components/LabelBox";
import { Spin } from "cargo-components/Spin";
import type {
  TableColumn,
  TablePinnedColumn,
  TableRecord,
} from "cargo-components/Table/types";
import { TypeIcon } from "cargo-components/Type/TypeIcon";
import { useResizeObserver, useStorageState } from "cargo-react-utils";
import { equals, fromEntries, stringToDate } from "cargo-utils";
import classNames from "classnames";
import { LazyTable } from "components/LazyTable";
import { LazyTooltip } from "components/LazyTooltip";
import { ApiContext } from "contexts/ApiContext";
import { RunBadge } from "modules/Orchestration/RunBadge/RunBadge";
import { RunNodeDetails } from "modules/Orchestration/RunNodeDetails/RunNodeDetails";
import { RunNodeDetailsTitle } from "modules/Orchestration/RunNodeDetails/RunNodeDetailsTitle/RunNodeDetailsTitle";
import { useRunNodeDetails } from "modules/Orchestration/RunNodeDetails/useRunNodeDetails";
import { WorkflowNodeIcon } from "modules/Orchestration/WorkflowNodeIcon/WorkflowNodeIcon";
import moment from "moment";
import React from "react";
import { useInfiniteQuery, useQuery } from "react-query";
import { getDatesDiff, useRouter } from "utils";

import { RecordBadge } from "../RecordBadge/RecordBadge";
import { WorkflowNodeTitle } from "../WorkflowNodeTitle/WorkflowNodeTitle";

const PAGE_SIZE = 100;
const META_COLUMN_SLUG = "$meta";
const RAN_AT_COLUMN_SLUG = "$ranAt";
const COLUMN_MIN_WIDTH = 100;

type SelectedRuns =
  | {
      kind: "uuids";
      uuids: string[];
    }
  | { kind: "all" };

type Props = {
  workflow: Orchestration.Workflow;
  workflowNodes: Orchestration.WorkflowNode[];
  selectedRuns: SelectedRuns;
  filters: {
    release?: Orchestration.Release;
    batch?: Orchestration.Batch;
    statuses?: Orchestration.RunStatus[];
    nodeSlugs?: string[];
    recordTitle?: string;
    groupNodeUuid?: string;
    executionsFilter?: Orchestration.ExecutionsFilter;
  };
  isLoading?: boolean;
  onSelectRuns: (selectedRuns: SelectedRuns) => void;
  onRunsFetch: (runsCount: number) => void;
};

export const RunsTable: React.FC<Props> = (props) => {
  const {
    workflow,
    workflowNodes,
    filters,
    selectedRuns,
    isLoading = false,
    onSelectRuns,
    onRunsFetch,
  } = props;

  const router = useRouter();
  const { api } = React.useContext(ApiContext);

  const [createdBefore, setCreatedBefore] = React.useState<Date>(new Date());

  const [highlightedCellId, setHighlightedCellId] = React.useState<
    string | undefined
  >(undefined);
  const [expandedNode, setExpandedNode] = React.useState<
    { nodeUuid: string; runUuid: string } | undefined
  >(undefined);

  const [areSystemColumnsExpanded, setAreSystemColumnsExpanded] =
    React.useState<boolean>(false);

  const isGroupBatch =
    filters.batch !== undefined &&
    filters.batch.data.kind === "runs" &&
    filters.batch.data.filter.isGroupParent === true;

  const areRecordsDisplayed = filters.batch === undefined;

  const filtersRunStatuses = filters.statuses;
  const filtersRecordStatuses = React.useMemo<
    Orchestration.RecordStatus[] | undefined
  >(() => {
    if (filters.statuses === undefined) {
      return undefined;
    }

    return filters.statuses.filter(
      (status): status is Orchestration.RecordStatus => {
        return status !== "idle" && status !== "skipped";
      },
    );
  }, [filters.statuses]);

  const recordsListPayload =
    React.useMemo<Orchestration.Api.ListRecordsPayload>(() => {
      return {
        workflowUuid: workflow.uuid,
        releaseUuid:
          filters.release !== undefined ? filters.release.uuid : undefined,
        batchUuid:
          filters.batch !== undefined &&
          (filters.groupNodeUuid === undefined || isGroupBatch === true)
            ? filters.batch.uuid
            : undefined,
        statuses: filtersRecordStatuses,
        title:
          filters.recordTitle !== undefined
            ? `%${filters.recordTitle}%`
            : undefined,
        isGroupParent: filters.groupNodeUuid !== undefined,
        parentBatchUuid:
          filters.batch !== undefined &&
          filters.groupNodeUuid !== undefined &&
          isGroupBatch === false
            ? filters.batch.uuid
            : undefined,
        parentNodeUuid: filters.groupNodeUuid,
        executionsFilter: filters.executionsFilter,
        createdBefore: createdBefore.toISOString(),
        limit: PAGE_SIZE,
      };
    }, [
      workflow.uuid,
      filters.release,
      filters.batch,
      filters.groupNodeUuid,
      filters.recordTitle,
      filters.executionsFilter,
      isGroupBatch,
      filtersRecordStatuses,
      createdBefore,
    ]);

  const recordsListState = useInfiniteQuery(
    Orchestration.recordKeys.list(workflow.workspaceUuid, recordsListPayload),
    (nextPageInput) =>
      api.orchestration.record.list(
        nextPageInput.pageParam || recordsListPayload,
      ),
    {
      enabled: areRecordsDisplayed === true,
      onSuccess: (result) => {
        const firstPage = result.pages[0];

        if (firstPage === undefined) {
          return;
        }

        onRunsFetch(firstPage.count);
      },
      refetchInterval: 10000,
      getNextPageParam: (lastPage, allPages) => {
        if (lastPage.records.length !== PAGE_SIZE) {
          return undefined;
        }

        return {
          ...recordsListPayload,
          offset: allPages
            .map((page) => {
              return page.records;
            })
            .flat().length,
        };
      },
    },
  );

  const runsListPayload =
    React.useMemo<Orchestration.Api.ListRunsPayload>(() => {
      return {
        workflowUuid: workflow.uuid,
        releaseUuid:
          filters.release !== undefined ? filters.release.uuid : undefined,
        batchUuid:
          filters.batch !== undefined &&
          (filters.groupNodeUuid === undefined || isGroupBatch === true)
            ? filters.batch.uuid
            : undefined,
        statuses: filtersRunStatuses,
        recordTitle:
          filters.recordTitle !== undefined
            ? `%${filters.recordTitle}%`
            : undefined,
        isGroupParent: filters.groupNodeUuid !== undefined,
        parentBatchUuid:
          filters.batch !== undefined &&
          filters.groupNodeUuid !== undefined &&
          isGroupBatch === false
            ? filters.batch.uuid
            : undefined,
        parentNodeUuid: filters.groupNodeUuid,
        executionsFilter: filters.executionsFilter,
        createdBefore: createdBefore.toISOString(),
        limit: PAGE_SIZE,
      };
    }, [
      workflow.uuid,
      filters.release,
      filters.batch,
      filters.groupNodeUuid,
      filters.recordTitle,
      filters.executionsFilter,
      isGroupBatch,
      filtersRunStatuses,
      createdBefore,
    ]);

  const runsListState = useInfiniteQuery(
    Orchestration.runKeys.list(workflow.workspaceUuid, runsListPayload),
    (nextPageInput) =>
      api.orchestration.run.list(nextPageInput.pageParam || runsListPayload),
    {
      enabled: areRecordsDisplayed === false,
      onSuccess: (result) => {
        const firstPage = result.pages[0];

        if (firstPage === undefined) {
          return;
        }

        onRunsFetch(firstPage.count);
      },
      refetchInterval: 10000,
      getNextPageParam: (lastPage, allPages) => {
        if (lastPage.runs.length !== PAGE_SIZE) {
          return undefined;
        }

        return {
          ...runsListPayload,
          offset: allPages
            .map((page) => {
              return page.runs;
            })
            .flat().length,
        };
      },
    },
  );

  const newRecordsCountPayload =
    React.useMemo<Orchestration.Api.CountRecordsPayload>(() => {
      return {
        workflowUuid: workflow.uuid,
        releaseUuid:
          filters.release !== undefined ? filters.release.uuid : undefined,
        batchUuid:
          filters.batch !== undefined &&
          (filters.groupNodeUuid === undefined || isGroupBatch === true)
            ? filters.batch.uuid
            : undefined,
        statuses: filtersRecordStatuses,
        title:
          filters.recordTitle !== undefined
            ? `%${filters.recordTitle}%`
            : undefined,
        isGroupParent: filters.groupNodeUuid !== undefined,
        parentBatchUuid:
          filters.batch !== undefined &&
          filters.groupNodeUuid !== undefined &&
          isGroupBatch === false
            ? filters.batch.uuid
            : undefined,
        parentNodeUuid: filters.groupNodeUuid,
        executionsFilter: filters.executionsFilter,
        createdAfter: createdBefore.toISOString(),
      };
    }, [
      workflow.uuid,
      filters.release,
      filters.batch,
      filters.groupNodeUuid,
      filters.recordTitle,
      filters.executionsFilter,
      isGroupBatch,
      filtersRecordStatuses,
      createdBefore,
    ]);

  const newRecordsCountState = useQuery(
    Orchestration.recordKeys.count(
      workflow.workspaceUuid,
      newRecordsCountPayload,
    ),
    () => api.orchestration.record.count(newRecordsCountPayload),
    {
      enabled: areRecordsDisplayed === true,
      refetchInterval: 10000,
    },
  );

  const newRunsCountPayload =
    React.useMemo<Orchestration.Api.CountRunsPayload>(() => {
      return {
        workflowUuid: workflow.uuid,
        releaseUuid:
          filters.release !== undefined ? filters.release.uuid : undefined,
        batchUuid:
          filters.batch !== undefined &&
          (filters.groupNodeUuid === undefined || isGroupBatch === true)
            ? filters.batch.uuid
            : undefined,
        statuses: filtersRunStatuses,
        recordTitle:
          filters.recordTitle !== undefined
            ? `%${filters.recordTitle}%`
            : undefined,
        isGroupParent: filters.groupNodeUuid !== undefined,
        parentBatchUuid:
          filters.batch !== undefined &&
          filters.groupNodeUuid !== undefined &&
          isGroupBatch === false
            ? filters.batch.uuid
            : undefined,
        parentNodeUuid: filters.groupNodeUuid,
        executionsFilter: filters.executionsFilter,
        createdAfter: createdBefore.toISOString(),
      };
    }, [
      workflow.uuid,
      filters.release,
      filters.batch,
      filters.groupNodeUuid,
      filters.recordTitle,
      filters.executionsFilter,
      isGroupBatch,
      filtersRunStatuses,
      createdBefore,
    ]);

  const newRunsCountState = useQuery(
    Orchestration.runKeys.count(workflow.workspaceUuid, newRunsCountPayload),
    () => api.orchestration.run.count(newRunsCountPayload),
    {
      enabled: areRecordsDisplayed === false,
      refetchInterval: 10000,
    },
  );

  React.useEffect(() => {
    onSelectRuns({ kind: "uuids", uuids: [] });
  }, [onSelectRuns, runsListPayload, recordsListPayload]);

  const listState =
    areRecordsDisplayed === true ? recordsListState : runsListState;
  const newCountState =
    areRecordsDisplayed === undefined
      ? newRecordsCountState
      : newRunsCountState;

  const runsOrRecords = React.useMemo(() => {
    if (listState.isSuccess === false) {
      return [];
    }

    const runsOrRecords = listState.data.pages
      .map((page) => {
        return "runs" in page ? page.runs : page.records;
      })
      .flat();

    return runsOrRecords;
  }, [listState]);

  const runsOrRecordsCount = React.useMemo(() => {
    if (listState.isSuccess === false) {
      return 0;
    }

    const firstPage = listState.data.pages[0];

    if (firstPage === undefined) {
      return 0;
    }

    return firstPage.count;
  }, [listState]);

  const newRunsOrRecordsCount = React.useMemo(() => {
    if (newCountState.isSuccess === false) {
      return 0;
    }

    const { count } = newCountState.data;

    return count;
  }, [newCountState]);

  React.useEffect(() => {
    if (runsOrRecordsCount === 0 && newRunsOrRecordsCount > 0) {
      setCreatedBefore(new Date());
    }
  }, [newRunsOrRecordsCount, runsOrRecordsCount]);

  const cachedSuccessfulExecutionIconByNodeReferenceRef = React.useRef<
    Record<string, React.ReactNode>
  >({});

  const getNodeReference = React.useCallback(
    (nodeSlug: string) => {
      if (filters.groupNodeUuid !== undefined) {
        return `${filters.groupNodeUuid}:${nodeSlug}`;
      }
      return nodeSlug;
    },
    [filters.groupNodeUuid],
  );

  const getSuccessfulExecutionIcon = React.useCallback(
    (execution: Orchestration.Execution): React.ReactNode => {
      const cachedIcon =
        cachedSuccessfulExecutionIconByNodeReferenceRef.current[
          getNodeReference(execution.nodeSlug)
        ];

      if (cachedIcon !== undefined) {
        return cachedIcon;
      }

      if (execution.iconUrl !== undefined) {
        const icon = (
          // eslint-disable-next-line @next/next/no-img-element
          <img
            alt=""
            src={execution.iconUrl}
            className="h-[24px] w-[24px] rounded-md bg-neutral-200 dark:bg-neutral-700"
          />
        );

        cachedSuccessfulExecutionIconByNodeReferenceRef.current[
          getNodeReference(execution.nodeSlug)
        ] = icon;

        return icon;
      }

      const workflowNode = workflowNodes.find((node) => {
        return node.slug === execution.nodeSlug;
      });

      if (workflowNode === undefined) {
        const icon = <TypeIcon type="any" className="h-[24px] w-[24px]" />;

        cachedSuccessfulExecutionIconByNodeReferenceRef.current[
          getNodeReference(execution.nodeSlug)
        ] = icon;

        return icon;
      }

      const icon = (
        <WorkflowNodeIcon
          workflow={workflow}
          node={workflowNode}
          isSubNode={filters.groupNodeUuid !== undefined}
        />
      );

      cachedSuccessfulExecutionIconByNodeReferenceRef.current[
        getNodeReference(execution.nodeSlug)
      ] = icon;

      return icon;
    },
    [filters.groupNodeUuid, getNodeReference, workflow, workflowNodes],
  );

  const [tableColumns, setTableColumns] = React.useState<TableColumn[]>([]);

  const lastColumnsDependenciesRef = React.useRef<{
    groupNodeUuid?: string;
    nodeSlugs?: string[];
    workflow: Orchestration.Workflow;
    workflowNodes: Orchestration.WorkflowNode[];
    areSystemColumnsExpanded: boolean;
  }>({
    groupNodeUuid: filters.groupNodeUuid,
    nodeSlugs: filters.nodeSlugs,
    workflow,
    workflowNodes,
    areSystemColumnsExpanded,
  });

  React.useEffect(() => {
    const dependencies = {
      groupNodeUuid: filters.groupNodeUuid,
      nodeSlugs: filters.nodeSlugs,
      workflow,
      workflowNodes,
      areSystemColumnsExpanded,
    };

    if (
      tableColumns.length > 0 &&
      lastColumnsDependenciesRef.current.areSystemColumnsExpanded ===
        areSystemColumnsExpanded &&
      equals(
        lastColumnsDependenciesRef.current.groupNodeUuid,
        dependencies.groupNodeUuid,
      ) === true &&
      equals(
        lastColumnsDependenciesRef.current.nodeSlugs,
        dependencies.nodeSlugs,
      ) === true &&
      equals(
        lastColumnsDependenciesRef.current.workflowNodes,
        dependencies.workflowNodes,
      ) === true
    ) {
      return;
    }

    lastColumnsDependenciesRef.current = dependencies;

    const nodeColumns = workflowNodes
      .filter((workflowNode) => {
        return (
          workflowNode.slug !== "start" &&
          (filters.nodeSlugs === undefined ||
            filters.nodeSlugs.includes(workflowNode.slug) === true)
        );
      })
      .map<TableColumn>((workflowNode) => {
        return {
          slug: workflowNode.slug,
          label: (
            <TableColumnLabel
              icon={
                <WorkflowNodeIcon
                  workflow={workflow}
                  node={workflowNode}
                  isSubNode={filters.groupNodeUuid !== undefined}
                />
              }
              label={<WorkflowNodeTitle node={workflowNode} />}
              slug={workflowNode.slug}
            />
          ),
        };
      });

    const workflowStartNode = workflow.nodes.find((node) => {
      return node.slug === "start";
    });

    const metaColumn: TableColumn = {
      slug: META_COLUMN_SLUG,
      label:
        workflowStartNode === undefined ? (
          ""
        ) : (
          <TableColumnLabel
            icon={
              <WorkflowNodeIcon
                workflow={workflow}
                node={workflowStartNode}
                isSubNode={filters.groupNodeUuid !== undefined}
              />
            }
            label={<WorkflowNodeTitle node={workflowStartNode} />}
            slug={workflowStartNode.slug}
            suffix={
              <LazyTooltip text="Toggle system columns">
                <Button
                  type="neutral"
                  size="xsmall"
                  icon={
                    <ChevronRightDouble
                      className={classNames(
                        "size-4",
                        areSystemColumnsExpanded === true
                          ? "rotate-180"
                          : undefined,
                      )}
                    />
                  }
                  onClick={() => {
                    setAreSystemColumnsExpanded(
                      (currentAreReadOnlyColumnsExpanded) => {
                        return currentAreReadOnlyColumnsExpanded === false;
                      },
                    );
                  }}
                />
              </LazyTooltip>
            }
          />
        ),
    };

    const ranAtColumn: TableColumn = {
      slug: RAN_AT_COLUMN_SLUG,
      label: (
        <TableColumnLabel
          icon={
            <div
              className={classNames(
                "flex size-6 items-center justify-center rounded-md border border-neutral-300/50 bg-white p-1 dark:border-neutral-700/50 dark:bg-neutral-900",
              )}
            >
              <BsCalendarDate />
            </div>
          }
          label="Ran at"
        />
      ),
      cellBackgroundColor: "blue",
    };

    const newTableColumns: TableColumn[] = [...nodeColumns, metaColumn];

    if (areSystemColumnsExpanded === true) {
      newTableColumns.push(ranAtColumn);
    }

    setTableColumns(newTableColumns);
  }, [
    areSystemColumnsExpanded,
    filters.groupNodeUuid,
    filters.nodeSlugs,
    tableColumns.length,
    workflow,
    workflowNodes,
  ]);

  const tablePinnedColumns = React.useMemo<TablePinnedColumn[]>(() => {
    const newTablePinnedColumns: TablePinnedColumn[] = [
      { slug: META_COLUMN_SLUG, position: "left" },
    ];

    if (areSystemColumnsExpanded === true) {
      newTablePinnedColumns.push({
        slug: RAN_AT_COLUMN_SLUG,
        position: "left",
      });
    }

    return newTablePinnedColumns;
  }, [areSystemColumnsExpanded]);

  const tableRecords = React.useMemo<TableRecord[]>(() => {
    if (filters.statuses !== undefined && filters.statuses.length === 0) {
      return [];
    }

    return runsOrRecords.map((runOrRecord) => {
      const executionEntries = runOrRecord.executions
        .filter((execution) => {
          return execution.nodeSlug !== "start";
        })
        .map<[string, React.ReactNode]>((execution) => {
          const expandExecutionNode = (cellId: string | undefined) => {
            setHighlightedCellId(cellId);
            setExpandedNode({
              nodeUuid: execution.nodeUuid,
              runUuid:
                "uuid" in runOrRecord ? runOrRecord.uuid : runOrRecord.runUuid,
            });
          };

          if (execution.status === "error") {
            return [
              execution.nodeSlug,
              <TableRecordCell
                key={execution.nodeUuid}
                icon={
                  <Badge color="red">
                    <VscError />
                  </Badge>
                }
                label={
                  execution.errorMessage !== undefined
                    ? execution.errorMessage
                    : "An error occurred"
                }
                onClick={expandExecutionNode}
              />,
            ];
          }

          if (execution.status === "success") {
            return [
              execution.nodeSlug,
              <TableRecordCell
                key={execution.nodeUuid}
                icon={getSuccessfulExecutionIcon(execution)}
                label={
                  execution.title !== undefined ? execution.title : "Executed"
                }
                onClick={expandExecutionNode}
              />,
            ];
          }

          if (runOrRecord.finishedAt === undefined) {
            return [
              execution.nodeSlug,
              <TableRecordCell
                key={execution.nodeUuid}
                icon={
                  <Badge color="grey">
                    <Spin size="small" />
                  </Badge>
                }
                label={
                  runOrRecord.status === "cancelling"
                    ? "Cancelling..."
                    : "Running..."
                }
              />,
            ];
          }

          return [execution.nodeSlug, TableDefaultCell];
        });

      const startDate = moment(runOrRecord.createdAt);
      const endDate =
        runOrRecord.finishedAt === undefined
          ? moment()
          : moment(runOrRecord.finishedAt);

      const timeToComplete = getDatesDiff(startDate, endDate);

      const castedTitle = stringToDate(
        "recordTitle" in runOrRecord
          ? runOrRecord.recordTitle
          : runOrRecord.title,
      );

      const startNodeExecution = runOrRecord.executions.find((execution) => {
        return execution.nodeSlug === "start";
      });

      const expandExecutionStartNode = (cellId: string | undefined) => {
        if (startNodeExecution === undefined) {
          return;
        }

        setHighlightedCellId(cellId);
        setExpandedNode({
          nodeUuid: startNodeExecution.nodeUuid,
          runUuid:
            "uuid" in runOrRecord ? runOrRecord.uuid : runOrRecord.runUuid,
        });
      };

      const metaCell = (
        <TableRecordCell
          key="meta"
          icon={
            "uuid" in runOrRecord ? (
              <RunBadge
                run={runOrRecord}
                withTooltip={true}
                withLabel={false}
              />
            ) : (
              <RecordBadge
                record={runOrRecord}
                withTooltip={true}
                withLabel={false}
              />
            )
          }
          label={
            castedTitle instanceof Date
              ? moment(castedTitle).format("MMM Do, YYYY [at] hh:mm A")
              : castedTitle
          }
          onClick={expandExecutionStartNode}
        />
      );

      const ranAtCell = (
        <TableRecordCell
          key="createdAt"
          label={`${moment(runOrRecord.createdAt).format(
            "MMM Do, YYYY [at] hh:mm A",
          )} (${timeToComplete})`}
        />
      );

      const tableRecord: TableRecord = {
        ...fromEntries(executionEntries),
        [META_COLUMN_SLUG]: metaCell,
      };

      if (areSystemColumnsExpanded === true) {
        tableRecord[RAN_AT_COLUMN_SLUG] = ranAtCell;
      }

      return tableRecord;
    });
  }, [
    filters.statuses,
    runsOrRecords,
    getSuccessfulExecutionIcon,
    areSystemColumnsExpanded,
  ]);

  const tableLoadingRecords = React.useMemo<TableRecord[]>(() => {
    return Array.from({ length: 1 }).map((_, index) => {
      return {
        ...fromEntries(
          workflow.nodes.map((node) => {
            return [node.slug, " "];
          }),
        ),
        [META_COLUMN_SLUG]: (
          <TableRecordCell
            key={index}
            icon={<Spin size="small" />}
            label={
              <span className="text-neutral-500 dark:text-neutral-400">
                Loading...
              </span>
            }
          />
        ),
      };
    });
  }, [workflow.nodes]);

  const tableSelectedIds = React.useMemo(() => {
    const runUuids = runsOrRecords.map((runOrRecord) => {
      return "uuid" in runOrRecord ? runOrRecord.uuid : runOrRecord.runUuid;
    });

    if (selectedRuns.kind === "all") {
      return runUuids.map((_, index) => {
        return index;
      });
    }

    return runUuids
      .map((runUuid, index) => {
        return selectedRuns.uuids.includes(runUuid) === true
          ? index
          : undefined;
      })
      .filter((index) => {
        return index !== undefined;
      }) as number[];
  }, [runsOrRecords, selectedRuns]);

  const tableOnExpand = React.useCallback(
    (id: number) => {
      const runOrRecord = runsOrRecords[id];

      if (runOrRecord === undefined) {
        return;
      }

      const updatedSearchParams = new URLSearchParams(router.searchParams);
      updatedSearchParams.set(
        "runUuid",
        "uuid" in runOrRecord ? runOrRecord.uuid : runOrRecord.runUuid,
      );

      router.workspace.updateSearchParams(updatedSearchParams);
    },
    [router.searchParams, router.workspace, runsOrRecords],
  );

  const tableOnSelectIds = React.useCallback(
    (ids: number[]) => {
      if (runsOrRecords.length === 0) {
        return;
      }

      onSelectRuns({
        kind: "uuids",
        uuids: ids
          .map((id) => {
            const runOrRecord = runsOrRecords[id];

            if (runOrRecord === undefined) {
              return undefined;
            }

            return "uuid" in runOrRecord
              ? runOrRecord.uuid
              : runOrRecord.runUuid;
          })
          .filter((runUuid): runUuid is string => {
            return runUuid !== undefined;
          }),
      });
    },
    [onSelectRuns, runsOrRecords],
  );

  const drawerRunNodeDetails = useRunNodeDetails({
    nodeUuid: expandedNode !== undefined ? expandedNode.nodeUuid : undefined,
    runUuid: expandedNode !== undefined ? expandedNode.runUuid : undefined,
  });

  const drawerTitle = React.useMemo<React.ReactNode>(() => {
    return <RunNodeDetailsTitle details={drawerRunNodeDetails} />;
  }, [drawerRunNodeDetails]);

  const drawerBody = React.useMemo<React.ReactNode>(() => {
    return <RunNodeDetails details={drawerRunNodeDetails} />;
  }, [drawerRunNodeDetails]);

  const [drawerSize, setDrawerSize] = useStorageState<{
    width: number;
    height: number;
  }>({
    key: "runsTable:drawer:size",
    defaultValue: {
      width: 400,
      height: 450,
    },
    storage: "localStorage",
  });

  const drawer = React.useMemo(() => {
    if (expandedNode === undefined) {
      return undefined;
    }

    return {
      title: drawerTitle,
      body: drawerBody,
      width: drawerSize.width,
      height: drawerSize.height,
      minWidth: 350,
      minHeight: 350,
      maxWidth: "40%" as const,
      padding: {
        top: 12,
        right: 12,
      },
      isResizable: true,
      onSizeChange: setDrawerSize,
      onClose: () => {
        setHighlightedCellId(undefined);
        setExpandedNode(undefined);
      },
    };
  }, [
    expandedNode,
    drawerTitle,
    drawerBody,
    drawerSize.width,
    drawerSize.height,
    setDrawerSize,
  ]);

  const isLoadingTable = isLoading === true || listState.isLoading === true;

  return (
    <React.Fragment>
      <div className="h-full w-full">
        {runsOrRecordsCount > 0 && newRunsOrRecordsCount > 0 ? (
          <button
            className="absolute left-0 right-0 top-[82px] z-layer-1 ml-auto mr-auto mt-1 flex w-fit flex-row items-center justify-center space-x-2 rounded-full border border-neutral-300/50 bg-neutral-50 px-2 py-1 hover:bg-neutral-100 dark:border-neutral-700/50 dark:bg-neutral-800 dark:hover:bg-neutral-700"
            onClick={() => {
              setCreatedBefore(new Date());
            }}
          >
            <span className="shrink-0">
              <IoIosArrowRoundUp />
            </span>
            <span className="min-w-0 flex-1">
              Load {newRunsOrRecordsCount} new runs
            </span>
          </button>
        ) : null}

        <LazyTable
          columns={tableColumns}
          records={isLoadingTable === true ? tableLoadingRecords : tableRecords}
          pinnedColumns={tablePinnedColumns}
          selectedIds={tableSelectedIds}
          highlightedCellId={highlightedCellId}
          defaultRecordCellValue={TableDefaultCell}
          className="h-full w-full"
          hasMore={listState.hasNextPage === true}
          hideIndex={true}
          isFetchingMore={listState.isFetchingNextPage === true}
          fetchMore={listState.fetchNextPage}
          onSelectIds={isLoadingTable === true ? undefined : tableOnSelectIds}
          onExpand={isLoadingTable === true ? undefined : tableOnExpand}
          columnMinWidth={COLUMN_MIN_WIDTH}
          drawer={drawer}
        />

        {determineIfIsFetcherError(listState.error) === true ? (
          <Empty
            className="h-full w-full"
            title="An error occurred"
            description={listState.error.message}
            buttonText="Retry"
            onClick={() => {
              listState.refetch();
            }}
          />
        ) : null}
      </div>
    </React.Fragment>
  );
};

type TableColumnLabelProps = {
  icon: React.ReactNode;
  label: React.ReactNode;
  slug?: string;
  suffix?: React.ReactNode;
};

const TableColumnLabel: React.FC<TableColumnLabelProps> = (props) => {
  const { icon, label, slug, suffix } = props;

  const containerElementRef = React.useRef<HTMLDivElement | null>(null);
  const labelElementRef = React.useRef<HTMLDivElement | null>(null);
  const slugParentElementRef = React.useRef<HTMLDivElement | null>(null);
  const slugElementRef = React.useRef<HTMLDivElement | null>(null);

  const lastContainerWidthRef = React.useRef<number | null>(null);

  const [isLabelCollapsed, setIsLabelCollapsed] =
    React.useState<boolean>(false);

  useResizeObserver(containerElementRef, (elementRect) => {
    if (
      slug === undefined ||
      slugElementRef.current === null ||
      slugParentElementRef.current === null ||
      elementRect.width === lastContainerWidthRef.current
    ) {
      return;
    }

    lastContainerWidthRef.current = elementRect.width;

    const WIDTH_THRESHOLD = 24; // roughly one letter + ellipsis

    const isLabelCollapsed =
      labelElementRef.current === null ||
      labelElementRef.current.clientWidth < WIDTH_THRESHOLD;

    const hasEnoughSpaceToShowLabelBack =
      slugParentElementRef.current !== null &&
      slugElementRef.current !== null &&
      slugParentElementRef.current.clientWidth -
        slugElementRef.current.clientWidth >
        WIDTH_THRESHOLD;

    setIsLabelCollapsed(
      isLabelCollapsed === true && hasEnoughSpaceToShowLabelBack === false,
    );
  });

  return (
    <div ref={containerElementRef} className="h-full w-full">
      <div
        className={classNames(
          "h-full w-full",
          "flex flex-row items-center justify-start space-x-2",
        )}
      >
        <div
          className={classNames(
            "h-full min-w-0 flex-1",
            "flex flex-row items-center justify-start space-x-2",
          )}
        >
          <div className="shrink-0">{icon}</div>

          {slug === undefined || isLabelCollapsed === false ? (
            <div ref={labelElementRef} className="min-w-0 flex-initial">
              <div className="truncate text-left font-medium">{label}</div>
            </div>
          ) : null}

          {slug !== undefined ? (
            <div
              ref={slugParentElementRef}
              className={isLabelCollapsed ? "min-w-0 flex-1" : "shrink-0"}
            >
              <div className="w-fit max-w-full">
                <LabelBox ref={slugElementRef} slug={slug} />
              </div>
            </div>
          ) : null}
        </div>

        {suffix !== undefined ? (
          <div
            className={classNames(
              "shrink-0",
              "flex flex-row items-center justify-end",
            )}
          >
            {suffix}
          </div>
        ) : null}
      </div>
    </div>
  );
};

type TableRecordCellProps = {
  icon?: React.ReactNode;
  label: React.ReactNode;
  cellId?: string; // injected by the Table component
  onClick?: (cellId: string | undefined) => void;
};

const TableRecordCell: React.FC<TableRecordCellProps> = (props) => {
  const { icon, label, cellId, onClick } = props;

  return (
    <div
      className={classNames(
        "flex h-full w-full flex-row items-center justify-start space-x-2 overflow-hidden",
        onClick !== undefined ? "cursor-pointer" : undefined,
      )}
      onClick={
        onClick !== undefined
          ? () => {
              onClick(cellId);
            }
          : undefined
      }
    >
      {icon !== undefined ? <div className="shrink-0">{icon}</div> : null}
      <div className="min-w-0 flex-1">
        {label === "" ? (
          <div className="truncate text-neutral-300 dark:text-neutral-600">
            Empty
          </div>
        ) : (
          <div className="truncate">{label}</div>
        )}
      </div>
    </div>
  );
};

const TableDefaultCell = <div />;
