import type { UseChatHelpers } from "@ai-sdk/react";
import type { UIMessage } from "ai";
import type { Ai } from "cargo-api";
import { equals } from "cargo-utils";
import { motion } from "framer-motion";
import React from "react";

import { ChatGreeting } from "../ChatGreeting/ChatGreeting";
import { ChatMessage } from "../ChatMessage/ChatMessage";
import { ChatThinkingMessage } from "../ChatThinkingMessage/ChatThinkingMessage";
import { useMessages } from "../hooks/useMessages";

type Props = {
  agent: Ai.Agent;
  release: Ai.Release;
  chatUuid: string;
  status: UseChatHelpers["status"];
  votes: Ai.Vote[];
  messages: UIMessage[];
  setMessages: UseChatHelpers["setMessages"];
  reload: UseChatHelpers["reload"];
  isReadonly: boolean;
};

const PureChatMessages: React.FC<Props> = (props) => {
  const {
    agent,
    release,
    chatUuid,
    status,
    votes,
    messages,
    setMessages,
    reload,
    isReadonly,
  } = props;

  const {
    containerRef: messagesContainerRef,
    endRef: messagesEndRef,
    onViewportEnter,
    onViewportLeave,
    hasSentMessage,
  } = useMessages({
    status,
    chatUuid,
  });

  const isThinking = React.useMemo(() => {
    const lastMessage = messages[messages.length - 1];

    return (
      status === "submitted" &&
      lastMessage !== undefined &&
      lastMessage.role === "user"
    );
  }, [messages, status]);

  return (
    <div
      ref={messagesContainerRef}
      className="relative flex min-w-0 flex-1 flex-col space-y-6 overflow-y-scroll pt-4"
    >
      {messages.length === 0 ? <ChatGreeting /> : null}

      {messages.map((message, index) => {
        return (
          <ChatMessage
            key={message.id}
            agent={agent}
            release={release}
            chatUuid={chatUuid}
            message={message}
            isLoading={status === "streaming" && messages.length - 1 === index}
            votes={votes.filter((vote) => vote.messageUuid === message.id)}
            setMessages={setMessages}
            reload={reload}
            isReadonly={isReadonly}
            requiresScrollPadding={
              hasSentMessage === true && index === messages.length - 1
            }
          />
        );
      })}

      {isThinking === true ? <ChatThinkingMessage agent={agent} /> : null}

      <motion.div
        ref={messagesEndRef}
        className="min-h-[24px] min-w-[24px] shrink-0"
        onViewportLeave={onViewportLeave}
        onViewportEnter={onViewportEnter}
      />
    </div>
  );
};

export const ChatMessages = React.memo(
  PureChatMessages,
  (prevProps, nextProps) => {
    if (prevProps.status !== nextProps.status) return false;
    if (prevProps.status && nextProps.status) return false;
    if (equals(prevProps.messages, nextProps.messages) === false) return false;
    if (equals(prevProps.votes, nextProps.votes) === false) return false;

    return true;
  },
);
