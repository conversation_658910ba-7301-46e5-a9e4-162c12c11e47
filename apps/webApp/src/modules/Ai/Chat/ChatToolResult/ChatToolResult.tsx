import Expand01 from "@untitled-ui/icons-react/build/esm/Expand01";
import Minimize01 from "@untitled-ui/icons-react/build/esm/Minimize01";
import type { Ai } from "cargo-api";
import { Button } from "cargo-components/Button";
import { InputSearch } from "cargo-components/InputSearch";
import { JsonViewer } from "cargo-components/JsonViewer";
import { ScreenBody } from "cargo-components/Screen/ScreenBody";
import { Title } from "cargo-components/Title";
import { ToolIcon } from "modules/Ai/ToolIcon";
import React from "react";

type Props = {
  tool: Ai.Tool;
  result: Record<string, unknown>;
};

export const ChatToolResult: React.FC<Props> = (props) => {
  const { tool, result } = props;

  const [isExpanded, setIsExpanded] = React.useState<boolean>(false);

  const [searchValue, setSearchValue] = React.useState<string | undefined>(
    undefined,
  );

  return (
    <div className="rounded-md border border-neutral-300/50 dark:border-neutral-700/50">
      <ScreenBody
        title={
          <div className="flex flex-row items-center space-x-2">
            <ToolIcon tool={tool} />
            <Title level={3}>{tool.name}</Title>
          </div>
        }
        className="max-h-[calc(50dvh)] overflow-auto p-2"
        header={
          <Button
            onClick={() => {
              setIsExpanded(!isExpanded);
            }}
            type="secondary"
            icon={
              isExpanded === true ? (
                <Expand01 className="size-3.5" />
              ) : (
                <Minimize01 className="size-3.5" />
              )
            }
          />
        }
        subHeader={
          isExpanded === true ? (
            <InputSearch
              className="my-2.5"
              value={searchValue}
              placeholder="Search..."
              onChange={(event) => {
                setSearchValue(event.target.value);
              }}
            />
          ) : null
        }
      >
        {isExpanded === true ? <JsonViewer data={result} /> : null}
      </ScreenBody>
    </div>
  );
};
