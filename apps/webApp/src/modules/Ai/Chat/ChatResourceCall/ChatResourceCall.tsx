import Expand01 from "@untitled-ui/icons-react/build/esm/Expand01";
import Minimize01 from "@untitled-ui/icons-react/build/esm/Minimize01";
import { type Ai, useStore } from "cargo-api";
import { Badge } from "cargo-components/Badge";
import { Button } from "cargo-components/Button";
import { ScreenBody } from "cargo-components/Screen/ScreenBody";
import { Spin } from "cargo-components/Spin";
import { Title } from "cargo-components/Title";
import { Form, useForm } from "components/Form";
import { ModelIcon } from "modules/Storage/ModelIcon/ModelIcon";
import React from "react";

type Props = {
  resource: Ai.Resource;
  config: Record<string, unknown>;
};

export const ChatResourceCall: React.FC<Props> = (props) => {
  const { resource, config } = props;

  const models = useStore((state) => state.models);

  const [isExpanded, setIsExpanded] = React.useState<boolean>(false);

  const model = React.useMemo(() => {
    return models.find((model) => model.uuid === resource.modelUuid);
  }, [models, resource.modelUuid]);

  const form = useForm<Record<string, unknown>>({
    initialData: config,
  });

  return (
    <div className="rounded-md border border-neutral-300/50 dark:border-neutral-700/50">
      <ScreenBody
        title={
          <div className="flex flex-row items-center space-x-2">
            {model !== undefined ? <ModelIcon model={model} /> : null}
            <Title level={3}>{resource.name}</Title>
          </div>
        }
        header={
          <div className="flex flex-row space-x-2">
            <Badge
              variant="light"
              color="grey"
              className="flex flex-row items-center space-x-1"
            >
              <Spin size="small" />
              <span>Running...</span>
            </Badge>
            <Button
              onClick={() => {
                setIsExpanded(!isExpanded);
              }}
              type="secondary"
              icon={
                isExpanded === true ? (
                  <Expand01 className="size-3.5" />
                ) : (
                  <Minimize01 className="size-3.5" />
                )
              }
            />
          </div>
        }
        className="p-2"
      >
        {isExpanded === true ? (
          <Form
            id={form.id}
            data={form.state.data}
            jsonSchema={{
              type: "object",
              properties: {
                question: {
                  title: "Question",
                  type: "string",
                },
              },
              required: ["question"],
            }}
            uiSchema={{}}
            isDisabled={true}
            onChange={form.state.setData}
          />
        ) : null}
      </ScreenBody>
    </div>
  );
};
