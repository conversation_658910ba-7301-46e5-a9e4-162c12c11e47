import { RxCross2 } from "@react-icons/all-files/rx/RxCross2";
import { Button } from "cargo-components/Button";
import React from "react";

import { initialArtifactData, useArtifact } from "../../hooks/useArtifact";

function PureChatArtifactCloseButton() {
  const { setArtifact } = useArtifact();

  return (
    <Button
      type="secondary"
      icon={<RxCross2 />}
      onClick={() => {
        setArtifact((currentArtifact) =>
          currentArtifact.status === "streaming"
            ? {
                ...currentArtifact,
                isVisible: false,
              }
            : { ...initialArtifactData, status: "idle" },
        );
      }}
    />
  );
}

export const ChatArtifactCloseButton = React.memo(
  PureChatArtifactCloseButton,
  () => true,
);
