"use client";

import type { Ai } from "cargo-api";
import { Button } from "cargo-components/Button";
import { isAfter } from "date-fns";
import { motion } from "framer-motion";
import React from "react";

import { getDocumentTimestampByIndex } from "@/lib/utils";

import { useArtifact } from "../../hooks/useArtifact";

type Props = {
  handleVersionChange: (type: "next" | "prev" | "toggle" | "latest") => void;
  documents: Ai.Document[];
  currentVersionIndex: number;
};

export const ChatArtifactVersionFooter: React.FC<Props> = (props) => {
  const { handleVersionChange, documents, currentVersionIndex } = props;

  const { artifact } = useArtifact();

  const 

  return (
    <motion.div
      className="z-50 absolute bottom-0 flex w-full flex-col justify-between gap-4 border-t p-4 lg:flex-row"
      initial={{ y: 77 }}
      animate={{ y: 0 }}
      exit={{ y: 77 }}
      transition={{ type: "spring", stiffness: 140, damping: 20 }}
    >
      <div>
        <div>You are viewing a previous version</div>
        <div className="text-muted-foreground text-sm">
          Restore this version to make edits
        </div>
      </div>

      <div className="flex flex-row gap-4">
        <Button
          isLoading={isMutating === true}
          onClick={async () => {
            setIsMutating(true);

            mutate(
              `/api/document?id=${artifact.documentId}`,
              await fetch(
                `/api/document?id=${artifact.documentId}&timestamp=${getDocumentTimestampByIndex(
                  documents,
                  currentVersionIndex,
                )}`,
                {
                  method: "DELETE",
                },
              ),
              {
                optimisticData: documents
                  ? [
                      ...documents.filter((document) =>
                        isAfter(
                          new Date(document.createdAt),
                          new Date(
                            getDocumentTimestampByIndex(
                              documents,
                              currentVersionIndex,
                            ),
                          ),
                        ),
                      ),
                    ]
                  : [],
              },
            );
          }}
        />
        <Button
          variant="outline"
          onClick={() => {
            handleVersionChange("latest");
          }}
        >
          Back to latest version
        </Button>
      </div>
    </motion.div>
  );
};
