import Copy02 from "@untitled-ui/icons-react/build/esm/Copy02";
import CornerDownLeft from "@untitled-ui/icons-react/build/esm/CornerDownLeft";
import CornerDownRight from "@untitled-ui/icons-react/build/esm/CornerDownRight";
import List from "@untitled-ui/icons-react/build/esm/List";
import MessageCircle01 from "@untitled-ui/icons-react/build/esm/MessageCircle01";
import Play from "@untitled-ui/icons-react/build/esm/Play";
import { v4 as uuidv4 } from "uuid";

import type { ArtifactDefinition } from "../definition";
import { Ai } from "cargo-api";
import { Skeleton } from "./Skeleton";
import { DiffView } from "./DiffView";

type Metadata = {
  suggestions: Array<Ai.DocumentSuggestion>;
};

export const buildTextArtifactDefinition = (): ArtifactDefinition<
  "text",
  Metadata
> => {
  return {
    kind: "text",
    description: "Useful for text content, like drafting essays and emails.",
    initialize: async ({ documentId, setMetadata }) => {
      const suggestions = await getSuggestions({ documentId });

      setMetadata({
        suggestions,
      });
    },
    onStreamPart: ({ streamPart, setMetadata, setArtifact }) => {
      if (streamPart.type === "suggestion") {
        setMetadata((metadata) => {
          return {
            suggestions: [
              ...metadata.suggestions,
              streamPart.content as unknown as Ai.DocumentSuggestion,
            ],
          };
        });
      }

      if (streamPart.type === "text-delta") {
        setArtifact((draftArtifact) => {
          return {
            ...draftArtifact,
            content: draftArtifact.content + (streamPart.content as string),
            isVisible:
              draftArtifact.status === "streaming" &&
              draftArtifact.content.length > 400 &&
              draftArtifact.content.length < 450
                ? true
                : draftArtifact.isVisible,
            status: "streaming",
          };
        });
      }
    },
    content: ({
      mode,
      status,
      content,
      isCurrentVersion,
      currentVersionIndex,
      onSaveContent,
      getDocumentContentById,
      isLoading,
      metadata,
    }) => {
      if (isLoading === true) {
        return <Skeleton />;
      }

      if (mode === "diff") {
        const oldContent = getDocumentContentById(currentVersionIndex - 1);
        const newContent = getDocumentContentById(currentVersionIndex);

        return <DiffView oldContent={oldContent} newContent={newContent} />;
      }

      return (
        <>
          <div className="flex flex-row px-4 py-8 md:p-20">
            <Editor
              content={content}
              suggestions={metadata ? metadata.suggestions : []}
              isCurrentVersion={isCurrentVersion}
              currentVersionIndex={currentVersionIndex}
              status={status}
              onSaveContent={onSaveContent}
            />

            {metadata &&
            metadata.suggestions &&
            metadata.suggestions.length > 0 ? (
              <div className="h-dvh w-12 shrink-0 md:hidden" />
            ) : null}
          </div>
        </>
      );
    },
    actions: [
      {
        icon: <ClockRewind className="size-4" />,
        description: "View changes",
        onClick: ({ handleVersionChange }) => {
          handleVersionChange("toggle");
        },
        isDisabled: ({ currentVersionIndex, setMetadata }) => {
          if (currentVersionIndex === 0) {
            return true;
          }

          return false;
        },
      },
      {
        icon: <CornerDownLeft className="size-4" />,
        description: "View Previous version",
        onClick: ({ handleVersionChange }) => {
          handleVersionChange("prev");
        },
        isDisabled: ({ currentVersionIndex }) => {
          if (currentVersionIndex === 0) {
            return true;
          }

          return false;
        },
      },
      {
        icon: <CornerDownRight className="size-4" />,
        description: "View Next version",
        onClick: ({ handleVersionChange }) => {
          handleVersionChange("next");
        },
        isDisabled: ({ isCurrentVersion }) => {
          if (isCurrentVersion) {
            return true;
          }

          return false;
        },
      },
      {
        icon: <Copy02 className="size-4" />,
        description: "Copy to clipboard",
        onClick: ({ content }) => {
          navigator.clipboard.writeText(content);
        },
      },
    ],
    toolbar: [
      {
        icon: <PenIcon />,
        description: "Add final polish",
        onClick: ({ appendMessage }) => {
          appendMessage({
            role: "user",
            content:
              "Please add final polish and check for grammar, add section titles for better structure, and ensure everything reads smoothly.",
          });
        },
      },
      {
        icon: <MessageCircle01 className="size-4" />,
        description: "Request suggestions",
        onClick: ({ appendMessage }) => {
          appendMessage({
            role: "user",
            content:
              "Please add suggestions you have that could improve the writing.",
          });
        },
      },
    ],
  };
};
