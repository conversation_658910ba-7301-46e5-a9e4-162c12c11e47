"use client";

import { IoClose } from "@react-icons/all-files/io5/IoClose";
import MessageCircle01 from "@untitled-ui/icons-react/build/esm/MessageCircle01";
import type { Ai } from "cargo-api";
import { Button } from "cargo-components/Button";
import { AnimatePresence, motion } from "framer-motion";
import React from "react";

type Props = {
  suggestion: Ai.DocumentSuggestion;
  onApply: () => void;
};

export const SuggestionPreview: React.FC<Props> = (props) => {
  const { suggestion, onApply } = props;

  const [isExpanded, setIsExpanded] = React.useState(false);

  return (
    <AnimatePresence>
      {isExpanded === false ? (
        <motion.div
          className="absolute -right-8 cursor-pointer p-1"
          onClick={() => {
            setIsExpanded(true);
          }}
          whileHover={{ scale: 1.1 }}
        >
          <MessageCircle01 className="size-4" />
        </motion.div>
      ) : (
        <motion.div
          key={suggestion.uuid}
          className="z-50 absolute -right-12 flex w-56 flex-col gap-3 rounded-2xl border p-3 font-sans text-sm shadow-xl md:-right-16"
          transition={{ type: "spring", stiffness: 500, damping: 30 }}
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: -20 }}
          exit={{ opacity: 0, y: -10 }}
          whileHover={{ scale: 1.05 }}
        >
          <div className="flex flex-row items-center justify-between">
            <div className="flex flex-row items-center gap-2">
              <div className="size-4 rounded-full" />
              <div className="font-medium">Assistant</div>
            </div>
            <button
              type="button"
              className="cursor-pointer text-xs text-gray-500"
              onClick={() => {
                setIsExpanded(false);
              }}
            >
              <IoClose className="size-4" />
            </button>
          </div>
          <div>{suggestion.description}</div>
          <Button type="secondary" className="!rounded-full" onClick={onApply}>
            Apply
          </Button>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
