"use client";

import "react-data-grid/lib/styles.css";

import classNames from "classnames";
import { useTheme } from "next-themes";
import { parse, unparse } from "papaparse";
import React from "react";
import DataGrid, { textEditor } from "react-data-grid";

type Props = {
  content: string;
  saveContent: (content: string, isCurrentVersion: boolean) => void;
  status: string;
  isCurrentVersion: boolean;
  currentVersionIndex: number;
};

const MIN_ROWS = 50;
const MIN_COLS = 26;

const PureEditor: React.FC<Props> = (props) => {
  const { content, saveContent } = props;

  const { resolvedTheme } = useTheme();

  const parseData = React.useMemo(() => {
    if (!content) return Array(MIN_ROWS).fill(Array(MIN_COLS).fill(""));
    const result = parse<string[]>(content, { skipEmptyLines: true });

    const paddedData = result.data.map((row) => {
      const paddedRow = [...row];
      while (paddedRow.length < MIN_COLS) {
        paddedRow.push("");
      }
      return paddedRow;
    });

    while (paddedData.length < MIN_ROWS) {
      paddedData.push(Array(MIN_COLS).fill(""));
    }

    return paddedData;
  }, [content]);

  const columns = React.useMemo(() => {
    const rowNumberColumn = {
      key: "rowNumber",
      name: "",
      frozen: true,
      width: 50,
      renderCell: ({ rowIdx }: { rowIdx: number }) => rowIdx + 1,
      cellClass: "border-t border-r dark:bg-zinc-950 dark:text-zinc-50",
      headerCellClass: "border-t border-r dark:bg-zinc-900 dark:text-zinc-50",
    };

    const dataColumns = Array.from({ length: MIN_COLS }, (_, i) => ({
      key: i.toString(),
      name: String.fromCharCode(65 + i),
      renderEditCell: textEditor,
      width: 120,
      cellClass: classNames(
        `border-t dark:bg-zinc-950 dark:text-zinc-50`,
        i !== 0 ? "border-l" : null,
      ),
      headerCellClass: classNames(
        `border-t dark:bg-zinc-900 dark:text-zinc-50`,
        i !== 0 ? "border-l" : null,
      ),
    }));

    return [rowNumberColumn, ...dataColumns];
  }, []);

  const initialRows = React.useMemo(() => {
    return parseData.map((row, rowIndex) => {
      const rowData: any = {
        id: rowIndex,
        rowNumber: rowIndex + 1,
      };

      columns.slice(1).forEach((col, colIndex) => {
        rowData[col.key] = row[colIndex] || "";
      });

      return rowData;
    });
  }, [parseData, columns]);

  const [localRows, setLocalRows] = React.useState(initialRows);

  React.useEffect(() => {
    setLocalRows(initialRows);
  }, [initialRows]);

  const generateCsv = (data: any[][]) => {
    return unparse(data);
  };

  const handleRowsChange = (newRows: any[]) => {
    setLocalRows(newRows);

    const updatedData = newRows.map((row) => {
      return columns.slice(1).map((col) => row[col.key] || "");
    });

    const newCsvContent = generateCsv(updatedData);
    saveContent(newCsvContent, true);
  };

  return (
    <DataGrid
      className={resolvedTheme === "dark" ? "rdg-dark" : "rdg-light"}
      columns={columns}
      rows={localRows}
      enableVirtualization
      onRowsChange={handleRowsChange}
      onCellClick={(args) => {
        if (args.column.key !== "rowNumber") {
          args.selectCell(true);
        }
      }}
      style={{ height: "100%" }}
      defaultColumnOptions={{
        resizable: true,
        sortable: true,
      }}
    />
  );
};

export const Editor = React.memo(
  PureEditor,
  (prevProps: Props, nextProps: Props) => {
    return (
      prevProps.currentVersionIndex === nextProps.currentVersionIndex &&
      prevProps.isCurrentVersion === nextProps.isCurrentVersion &&
      !(prevProps.status === "streaming" && nextProps.status === "streaming") &&
      prevProps.content === nextProps.content &&
      prevProps.saveContent === nextProps.saveContent
    );
  },
);
