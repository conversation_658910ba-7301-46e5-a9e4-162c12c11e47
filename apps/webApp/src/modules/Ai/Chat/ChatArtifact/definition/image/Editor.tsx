import { Spin } from "cargo-components/Spin";
import classNames from "classnames";

type Props = {
  title: string;
  content: string;
  isCurrentVersion: boolean;
  currentVersionIndex: number;
  status: string;
  isInline: boolean;
};

export const Editor: React.FC<Props> = (props) => {
  const { title, content, status, isInline } = props;

  return (
    <div
      className={classNames(
        "flex w-full flex-row items-center justify-center",
        isInline === true ? "h-[200px]" : "h-[calc(100dvh-60px)]",
      )}
    >
      {status === "streaming" ? (
        <div className="flex flex-row items-center gap-4">
          {isInline === false ? (
            <div className="animate-spin">
              <Spin size="middle" />
            </div>
          ) : null}
          <div>Generating Image...</div>
        </div>
      ) : (
        <picture>
          <img
            className={classNames(
              "h-fit w-full max-w-[800px]",
              isInline === false ? "p-0 md:p-20" : null,
            )}
            src={`data:image/png;base64,${content}`}
            alt={title}
          />
        </picture>
      )}
    </div>
  );
};
