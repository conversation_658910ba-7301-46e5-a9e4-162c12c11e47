import { IoClose } from "@react-icons/all-files/io5/IoClose";
import TerminalSquare from "@untitled-ui/icons-react/build/esm/TerminalSquare";
import { Button } from "cargo-components/Button";
import { Spin } from "cargo-components/Spin";
import classNames from "classnames";
import { useArtifactSelector } from "modules/Ai/Chat/hooks/useArtifact";
import React from "react";

export type ConsoleOutputContent = {
  type: "text" | "image";
  value: string;
};

export type ConsoleOutput = {
  id: string;
  status: "in_progress" | "loading_packages" | "completed" | "failed";
  contents: Array<ConsoleOutputContent>;
};

type Props = {
  consoleOutputs: ConsoleOutput[];
  setConsoleOutputs: React.Dispatch<React.SetStateAction<ConsoleOutput[]>>;
};

export const Console: React.FC<Props> = (props) => {
  const { consoleOutputs, setConsoleOutputs } = props;

  const [height, setHeight] = React.useState<number>(300);
  const [isResizing, setIsResizing] = React.useState(false);
  const consoleEndRef = React.useRef<HTMLDivElement>(null);

  const isArtifactVisible = useArtifactSelector((state) => state.isVisible);

  const minHeight = 100;
  const maxHeight = 800;

  const startResizing = React.useCallback(() => {
    setIsResizing(true);
  }, []);

  const stopResizing = React.useCallback(() => {
    setIsResizing(false);
  }, []);

  const resize = React.useCallback(
    (e: MouseEvent) => {
      if (isResizing) {
        const newHeight = window.innerHeight - e.clientY;
        if (newHeight >= minHeight && newHeight <= maxHeight) {
          setHeight(newHeight);
        }
      }
    },
    [isResizing],
  );

  React.useEffect(() => {
    window.addEventListener("mousemove", resize);
    window.addEventListener("mouseup", stopResizing);
    return () => {
      window.removeEventListener("mousemove", resize);
      window.removeEventListener("mouseup", stopResizing);
    };
  }, [resize, stopResizing]);

  React.useEffect(() => {
    consoleEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [consoleOutputs]);

  React.useEffect(() => {
    if (isArtifactVisible === false) {
      setConsoleOutputs([]);
    }
  }, [isArtifactVisible, setConsoleOutputs]);

  return consoleOutputs.length > 0 ? (
    <>
      <div
        className="z-50 fixed h-2 w-full cursor-ns-resize"
        onMouseDown={startResizing}
        style={{ bottom: height - 4 }}
        role="slider"
        aria-valuenow={minHeight}
      />

      <div
        className={classNames(
          "z-40 fixed bottom-0 flex w-full flex-col overflow-x-hidden overflow-y-scroll border-t border-neutral-300/50 dark:border-neutral-700/50",
          isResizing === true ? "select-none" : null,
        )}
        style={{ height }}
      >
        <div className="z-50 bg-muted sticky top-0 flex h-fit w-full flex-row items-center justify-between border-b border-neutral-300/50 px-2 py-1 dark:border-neutral-700/50">
          <div className="flex flex-row items-center gap-3 pl-2 text-sm">
            <div className="text-muted-foreground">
              <TerminalSquare />
            </div>
            <div>Console</div>
          </div>
          <Button
            size="small"
            type="white"
            icon={<IoClose />}
            onClick={() => setConsoleOutputs([])}
          />
        </div>

        <div>
          {consoleOutputs.map((consoleOutput, index) => (
            <div
              key={consoleOutput.id}
              className="flex flex-row border-b border-neutral-300/50 px-4 py-2 font-mono text-sm dark:border-neutral-700/50"
            >
              <div
                className={classNames("w-12 shrink-0", {
                  "text-muted-foreground": [
                    "in_progress",
                    "loading_packages",
                  ].includes(consoleOutput.status),
                  "text-emerald-500": consoleOutput.status === "completed",
                  "text-red-400": consoleOutput.status === "failed",
                })}
              >
                [{index + 1}]
              </div>
              {["in_progress", "loading_packages"].includes(
                consoleOutput.status,
              ) ? (
                <div className="flex flex-row gap-2">
                  <div className="mb-auto mt-0.5 size-fit animate-spin self-center">
                    <Spin size="small" />
                  </div>
                  <div className="text-muted-foreground">
                    {consoleOutput.status === "in_progress"
                      ? "Initializing..."
                      : consoleOutput.status === "loading_packages"
                        ? consoleOutput.contents.map((content) =>
                            content.type === "text" ? content.value : null,
                          )
                        : null}
                  </div>
                </div>
              ) : (
                <div className="flex w-full flex-col gap-2 overflow-x-scroll">
                  {consoleOutput.contents.map((content, index) =>
                    content.type === "image" ? (
                      <picture key={`${consoleOutput.id}-${index}`}>
                        <img
                          src={content.value}
                          alt="output"
                          className="max-w-screen-toast-mobile w-full rounded-md"
                        />
                      </picture>
                    ) : (
                      <div
                        key={`${consoleOutput.id}-${index}`}
                        className="w-full whitespace-pre-line break-words"
                      >
                        {content.value}
                      </div>
                    ),
                  )}
                </div>
              )}
            </div>
          ))}
          <div ref={consoleEndRef} />
        </div>
      </div>
    </>
  ) : null;
};
