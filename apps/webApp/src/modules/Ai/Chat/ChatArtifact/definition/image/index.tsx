import Copy02 from "@untitled-ui/icons-react/build/esm/Copy02";
import CornerDownLeft from "@untitled-ui/icons-react/build/esm/CornerDownLeft";
import CornerDownRight from "@untitled-ui/icons-react/build/esm/CornerDownRight";

import type { ArtifactDefinition } from "../definition";
import { Editor } from "./Editor";

export const buildImageArtifactDefinition = (): ArtifactDefinition<"image"> => {
  return {
    kind: "image",
    description: "Useful for image generation",
    initialize: () => {},
    onStreamPart: ({ streamPart, setArtifact }) => {
      if (streamPart.type === "image-delta") {
        setArtifact((draftArtifact) => ({
          ...draftArtifact,
          content: streamPart.content as string,
          isVisible: true,
          status: "streaming",
        }));
      }
    },
    content: (props) => <Editor {...props} />,
    actions: [
      {
        icon: <CornerDownLeft className="size-4" />,
        description: "View Previous version",
        onClick: ({ handleVersionChange }) => {
          handleVersionChange("prev");
        },
        isDisabled: ({ currentVersionIndex }) => {
          if (currentVersionIndex === 0) {
            return true;
          }

          return false;
        },
      },
      {
        icon: <CornerDownRight className="size-4" />,
        description: "View Next version",
        onClick: ({ handleVersionChange }) => {
          handleVersionChange("next");
        },
        isDisabled: ({ isCurrentVersion }) => {
          if (isCurrentVersion) {
            return true;
          }

          return false;
        },
      },
      {
        icon: <Copy02 className="size-4" />,
        description: "Copy image to clipboard",
        onClick: ({ content }) => {
          const img = new Image();
          img.src = `data:image/png;base64,${content}`;

          img.onload = () => {
            const canvas = document.createElement("canvas");
            canvas.width = img.width;
            canvas.height = img.height;
            const ctx = canvas.getContext("2d");
            ctx?.drawImage(img, 0, 0);
            canvas.toBlob((blob) => {
              if (blob) {
                navigator.clipboard.write([
                  new ClipboardItem({ "image/png": blob }),
                ]);
              }
            }, "image/png");
          };
        },
      },
    ],
    toolbar: [],
  };
};
