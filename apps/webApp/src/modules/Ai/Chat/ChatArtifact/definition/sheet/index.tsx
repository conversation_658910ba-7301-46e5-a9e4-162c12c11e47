import Copy02 from "@untitled-ui/icons-react/build/esm/Copy02";
import CornerDownLeft from "@untitled-ui/icons-react/build/esm/CornerDownLeft";
import CornerDownRight from "@untitled-ui/icons-react/build/esm/CornerDownRight";
import LineChartUp01 from "@untitled-ui/icons-react/build/esm/LineChartUp01";
import Stars02 from "@untitled-ui/icons-react/build/esm/Stars02";
import { parse, unparse } from "papaparse";

import type { ArtifactDefinition } from "../definition";
import { Editor } from "./Editor";

type Metadata = any;

export const buildSheetArtifactDefinition = (): ArtifactDefinition<
  "sheet",
  Metadata
> => {
  return {
    kind: "sheet",
    description: "Useful for working with spreadsheets",
    initialize: async () => {},
    onStreamPart: ({ setArtifact, streamPart }) => {
      if (streamPart.type === "sheet-delta") {
        setArtifact((draftArtifact) => ({
          ...draftArtifact,
          content: streamPart.content as string,
          isVisible: true,
          status: "streaming",
        }));
      }
    },
    content: ({
      content,
      currentVersionIndex,
      isCurrentVersion,
      onSaveContent,
      status,
    }) => {
      return (
        <Editor
          content={content}
          currentVersionIndex={currentVersionIndex}
          isCurrentVersion={isCurrentVersion}
          saveContent={onSaveContent}
          status={status}
        />
      );
    },
    actions: [
      {
        icon: <CornerDownLeft className="size-4" />,
        description: "View Previous version",
        onClick: ({ handleVersionChange }) => {
          handleVersionChange("prev");
        },
        isDisabled: ({ currentVersionIndex }) => {
          if (currentVersionIndex === 0) {
            return true;
          }

          return false;
        },
      },
      {
        icon: <CornerDownRight className="size-4" />,
        description: "View Next version",
        onClick: ({ handleVersionChange }) => {
          handleVersionChange("next");
        },
        isDisabled: ({ isCurrentVersion }) => {
          if (isCurrentVersion) {
            return true;
          }

          return false;
        },
      },
      {
        icon: <Copy02 className="size-4" />,
        description: "Copy as .csv",
        onClick: ({ content }) => {
          const parsed = parse<string[]>(content, { skipEmptyLines: true });

          const nonEmptyRows = parsed.data.filter((row) =>
            row.some((cell) => cell.trim() !== ""),
          );

          const cleanedCsv = unparse(nonEmptyRows);

          navigator.clipboard.writeText(cleanedCsv);
        },
      },
    ],
    toolbar: [
      {
        description: "Format and clean data",
        icon: <Stars02 className="size-4" />,
        onClick: ({ appendMessage }) => {
          appendMessage({
            role: "user",
            content: "Can you please format and clean the data?",
          });
        },
      },
      {
        description: "Analyze and visualize data",
        icon: <LineChartUp01 className="size-4" />,
        onClick: ({ appendMessage }) => {
          appendMessage({
            role: "user",
            content:
              "Can you please analyze and visualize the data by creating a new code artifact in python?",
          });
        },
      },
    ],
  };
};
