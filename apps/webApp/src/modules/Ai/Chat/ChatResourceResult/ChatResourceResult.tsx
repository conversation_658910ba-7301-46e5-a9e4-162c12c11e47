import Expand01 from "@untitled-ui/icons-react/build/esm/Expand01";
import Minimize01 from "@untitled-ui/icons-react/build/esm/Minimize01";
import { type Ai, useStore } from "cargo-api";
import { Button } from "cargo-components/Button";
import { ScreenBody } from "cargo-components/Screen/ScreenBody";
import { type TableRecord } from "cargo-components/Table/types";
import { Title } from "cargo-components/Title";
import { getKeys, stringifyJson } from "cargo-utils";
import { LazyTable } from "components/LazyTable";
import { ModelIcon } from "modules/Storage/ModelIcon/ModelIcon";
import React from "react";

type Props = {
  resource: Ai.Resource;
  result: {
    records?: Record<string, unknown>[];
  };
};

export const ChatResourceResult: React.FC<Props> = (props) => {
  const { resource, result } = props;

  const models = useStore((state) => state.models);

  const [isExpanded, setIsExpanded] = React.useState<boolean>(false);

  const model = React.useMemo(() => {
    return models.find((model) => model.uuid === resource.modelUuid);
  }, [models, resource.modelUuid]);

  const records = React.useMemo(() => {
    return result.records !== undefined ? result.records : [];
  }, [result.records]);

  const tableColumns = React.useMemo(() => {
    const [firstRecord] = records;

    if (firstRecord === undefined) {
      return [];
    }

    return getKeys(firstRecord).map((key) => {
      return {
        label: (
          <div className="flex h-full w-full flex-row items-center justify-start">
            <div className="min-w-0 flex-1">
              <div className="truncate text-left font-medium">{key}</div>
            </div>
          </div>
        ),
        slug: key,
        size: 200,
      };
    });
  }, [records]);

  const tableRecords = React.useMemo(() => {
    if (records.length === 0) {
      return [];
    }

    return records.map<TableRecord>((record) => {
      return Object.entries(record).reduce<TableRecord>(
        (currentReshapedRecord, [key, value]) => {
          currentReshapedRecord[key] = <>{stringifyJson(value)}</>;

          return currentReshapedRecord;
        },
        {},
      );
    });
  }, [records]);

  return (
    <div className="rounded-md border border-neutral-300/50 dark:border-neutral-700/50">
      <ScreenBody
        title={
          <div className="flex flex-row items-center space-x-2">
            {model !== undefined ? <ModelIcon model={model} /> : null}
            <Title level={3}>{resource.name}</Title>
          </div>
        }
        className="max-h-[calc(30dvh)]"
        header={
          <Button
            onClick={() => {
              setIsExpanded(!isExpanded);
            }}
            type="secondary"
            icon={
              isExpanded === true ? (
                <Expand01 className="size-3.5" />
              ) : (
                <Minimize01 className="size-3.5" />
              )
            }
          />
        }
      >
        {isExpanded === true ? (
          <LazyTable
            columns={tableColumns}
            records={tableRecords}
            className="h-full w-full"
          />
        ) : null}
      </ScreenBody>
    </div>
  );
};
