"use client";

import ChevronDown from "@untitled-ui/icons-react/build/esm/ChevronDown";
import { Spin } from "cargo-components/Spin";
import { LazyMarkdown } from "components/LazyMarkdown";
import { AnimatePresence, motion } from "framer-motion";
import React from "react";

type Props = {
  isLoading: boolean;
  reasoning: string;
};

export const ChatMessageReasoning: React.FC<Props> = (props) => {
  const { isLoading, reasoning } = props;

  const [isExpanded, setIsExpanded] = React.useState<boolean>(true);

  const variants = {
    collapsed: {
      height: 0,
      opacity: 0,
      marginTop: 0,
      marginBottom: 0,
    },
    expanded: {
      height: "auto",
      opacity: 1,
      marginTop: "1rem",
      marginBottom: "0.5rem",
    },
  };

  return (
    <div className="flex flex-col">
      {isLoading === true ? (
        <div className="flex flex-row items-center space-x-2">
          <div className="font-medium">Reasoning</div>
          <div>
            <Spin size="xsmall" />
          </div>
        </div>
      ) : (
        <div className="flex flex-row items-center space-x-2">
          <div className="font-medium">Reasoned for a few seconds</div>
          <button
            type="button"
            className="cursor-pointer"
            onClick={() => {
              setIsExpanded(!isExpanded);
            }}
          >
            <ChevronDown className="size-3.5" />
          </button>
        </div>
      )}

      <AnimatePresence initial={false}>
        {isExpanded === true ? (
          <motion.div
            key="content"
            initial="collapsed"
            animate="expanded"
            exit="collapsed"
            variants={variants}
            transition={{ duration: 0.2, ease: "easeInOut" }}
            style={{ overflow: "hidden" }}
            className="flex flex-col gap-4 border-l pl-4 text-neutral-500 dark:text-neutral-400"
          >
            <LazyMarkdown>{reasoning}</LazyMarkdown>
          </motion.div>
        ) : null}
      </AnimatePresence>
    </div>
  );
};
