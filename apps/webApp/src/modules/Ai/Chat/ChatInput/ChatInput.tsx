"use client";
import type { UseChatHelpers } from "@ai-sdk/react";
import ArrowDown from "@untitled-ui/icons-react/build/esm/ArrowDown";
import type { Attachment, UIMessage } from "ai";
import type { Ai } from "cargo-api";
import { useStore } from "cargo-api";
import { Button } from "cargo-components/Button";
import { Textarea } from "cargo-components/Textarea";
import { useScrollToBottom } from "cargo-react-utils";
import classNames from "classnames";
import { config } from "config";
import { ApiContext } from "contexts/ApiContext";
import { NotificationContext } from "contexts/NotificationContext";
import { AnimatePresence, motion } from "framer-motion";
import React from "react";
import {
  type ChangeEvent,
  type Dispatch,
  type SetStateAction,
  useRef,
  useState,
} from "react";

import { ChatPreviewAttachment } from "../ChatPreviewAttachment/ChatPreviewAttachment";
import { ChatSuggestedActions } from "../ChatSuggestedActions/ChatSuggestedActions";
import { ChatInputAttachmentsButton } from "./ChatInputAttachmentsButton/ChatInputAttachmentsButton";
import { ChatInputSendButton } from "./ChatInputSendButton/ChatInputSendButton";
import { ChatInputStopButton } from "./ChatInputStopButton/ChatInputStopButton";

type Props = {
  release: Ai.Release;
  input: UseChatHelpers["input"];
  setInput: UseChatHelpers["setInput"];
  status: UseChatHelpers["status"];
  stop: () => void;
  attachments: Attachment[];
  setAttachments: Dispatch<SetStateAction<Attachment[]>>;
  messages: Array<UIMessage>;
  setMessages: UseChatHelpers["setMessages"];
  append: UseChatHelpers["append"];
  handleSubmit: UseChatHelpers["handleSubmit"];
  toolbar?: React.ReactNode;
  className?: string;
};

export const ChatInput: React.FC<Props> = (props) => {
  const {
    release,
    input,
    setInput,
    status,
    stop,
    attachments,
    setAttachments,
    messages,
    setMessages,
    append,
    handleSubmit,
    toolbar,
    className,
  } = props;

  const tokens = useStore((state) => state.tokens);

  const { addNotification } = React.useContext(NotificationContext);
  const { api } = React.useContext(ApiContext);

  const textareaRef = useRef<HTMLTextAreaElement>(null);

  React.useEffect(() => {
    if (textareaRef.current) {
      adjustHeight();
    }
  }, []);

  const adjustHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight + 2}px`;
    }
  };

  const resetHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
    }
  };

  const handleInput = React.useCallback(
    (value?: string) => {
      setInput(value !== undefined ? value : "");
      adjustHeight();
    },
    [setInput],
  );

  const token = React.useMemo<string | undefined>(() => {
    const token = tokens.find((token) => {
      return token.userUuid === undefined;
    });

    if (token === undefined) {
      return undefined;
    }

    return token.token;
  }, [tokens]);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadQueue, setUploadQueue] = useState<string[]>([]);

  const submitForm = React.useCallback(() => {
    handleSubmit(undefined, {
      experimental_attachments: attachments,
    });

    setAttachments([]);
    resetHeight();
  }, [attachments, handleSubmit, setAttachments]);

  const uploadFile = React.useCallback(
    (file: File) => {
      api.workspaceManagement.file.upload({
        file,
        onProgress: () => undefined,
        onComplete: ({ s3Filename, name, contentType }) => {
          const attachement = {
            url: `${config.api.baseUrl}/v1/workspaceManagement/files/fetch?s3Filename=${s3Filename}&token=${token}`,
            contentType,
            name,
          };

          setAttachments((currentAttachments) => [
            ...currentAttachments,
            attachement,
          ]);

          setUploadQueue((uploadQueue) =>
            uploadQueue.filter((name) => name !== file.name),
          );
        },
        onError: () => {
          setUploadQueue((uploadQueue) =>
            uploadQueue.filter((name) => name !== file.name),
          );
        },
      });
    },
    [api.workspaceManagement.file, setAttachments, token],
  );

  const handleFileChange = React.useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      if (event.target.files === null) {
        return;
      }

      const files = Array.from(event.target.files);

      setUploadQueue(files.map((file) => file.name));

      for (const file of files) {
        uploadFile(file);
      }
    },
    [uploadFile],
  );

  const { isAtBottom, scrollToBottom } = useScrollToBottom();

  React.useEffect(() => {
    if (status === "submitted") {
      scrollToBottom();
    }
  }, [status, scrollToBottom]);

  return (
    <div className="relative flex w-full flex-col gap-4">
      <AnimatePresence>
        {messages.length > 0 && isAtBottom === false ? (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
            className="z-50 absolute bottom-28 left-1/2 -translate-x-1/2"
          >
            <Button
              className="!rounded-full"
              type="secondary"
              onClick={() => {
                scrollToBottom();
              }}
              icon={<ArrowDown className="size-3.5" />}
            />
          </motion.div>
        ) : null}
      </AnimatePresence>

      {messages.length === 0 &&
      attachments.length === 0 &&
      uploadQueue.length === 0 ? (
        <ChatSuggestedActions release={release} append={append} />
      ) : null}

      <input
        type="file"
        className="pointer-events-none fixed -left-4 -top-4 size-0.5 opacity-0"
        ref={fileInputRef}
        multiple
        accept=".png, .jpeg, .jpg, .pdf, .doc, .docx, .txt"
        onChange={handleFileChange}
        tabIndex={-1}
      />

      {(attachments.length > 0 || uploadQueue.length > 0) && (
        <div
          data-testid="attachments-preview"
          className="flex flex-row items-end space-x-2 overflow-x-scroll"
        >
          {attachments.map((attachment) => (
            <ChatPreviewAttachment
              key={attachment.url}
              attachment={attachment}
            />
          ))}

          {uploadQueue.map((filename) => (
            <ChatPreviewAttachment
              key={filename}
              attachment={{
                url: "",
                name: filename,
                contentType: "",
              }}
              isUploading={true}
            />
          ))}
        </div>
      )}

      <Textarea
        forwardedRef={textareaRef}
        placeholder="Send a message..."
        value={input}
        onChange={handleInput}
        textareaClassName={classNames(
          "max-h-[calc(75dvh)] min-h-[24px] resize-none overflow-hidden rounded-2xl pb-10",
          className,
        )}
        rows={2}
        autoFocus
        onKeyDown={(event) => {
          if (
            event.key === "Enter" &&
            event.shiftKey === false &&
            event.nativeEvent.isComposing === false
          ) {
            event.preventDefault();

            if (status === "ready" || status === "error") {
              submitForm();

              return;
            }

            addNotification({
              title: "Please wait for the model to finish its response",
              type: "error",
            });
          }
        }}
      />

      <div className="absolute bottom-0 flex w-full flex-row space-x-2 p-2">
        <div className="shrink-0">
          <ChatInputAttachmentsButton
            fileInputRef={fileInputRef}
            status={status}
          />
        </div>
        <div className="min-w-0 flex-1">{toolbar}</div>
        <div className="shrink-0">
          {status === "submitted" ? (
            <ChatInputStopButton stop={stop} setMessages={setMessages} />
          ) : (
            <ChatInputSendButton
              input={input}
              submitForm={submitForm}
              uploadQueue={uploadQueue}
            />
          )}
        </div>
      </div>
    </div>
  );
};
