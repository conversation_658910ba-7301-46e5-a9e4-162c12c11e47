import { AiOutlineStop } from "@react-icons/all-files/ai/AiOutlineStop";
import { FiDownload } from "@react-icons/all-files/fi/FiDownload";
import { IoIosMore } from "@react-icons/all-files/io/IoIosMore";
import { IoRefreshOutline } from "@react-icons/all-files/io5/IoRefreshOutline";
import AlignJustify from "@untitled-ui/icons-react/build/esm/AlignJustify";
import type { Orchestration } from "cargo-api";
import { Button } from "cargo-components/Button";
import { Dropdown } from "cargo-components/Dropdown";
import { DropdownGroup } from "cargo-components/Dropdown/DropdownGroup";
import { DropdownItem } from "cargo-components/Dropdown/DropdownItem";
import { InputSearch } from "cargo-components/InputSearch";
import { LabelBox } from "cargo-components/LabelBox";
import { Tabs } from "cargo-components/Tabs";
import { TabsItem } from "cargo-components/Tabs/TabsItem";
import { useDeferredUrlState } from "cargo-react-utils";
import { ApiContext } from "contexts/ApiContext";
import { NotificationContext } from "contexts/NotificationContext";
import { ExecutionsFilterDropdown } from "modules/Orchestration/ExecutionsFilterDropdown";
import { ReleaseContext } from "modules/Orchestration/ReleaseContext";
import { ReleasesDropdown } from "modules/Orchestration/ReleasesDropdown/ReleasesDropdown";
import { RunsCancelModal } from "modules/Orchestration/RunsCancelModal/RunsCancelModal";
import { RunsRetryModal } from "modules/Orchestration/RunsRetryModal/RunsRetryModal";
import { RunsTable } from "modules/Orchestration/RunsTable";
import { RunStatusesDropdown } from "modules/Orchestration/RunStatusesDropdown/RunStatusesDropdown";
import { WorkflowNodeIcon } from "modules/Orchestration/WorkflowNodeIcon/WorkflowNodeIcon";
import { WorkflowNodesDropdown } from "modules/Orchestration/WorkflowNodesDropdown/WorkflowNodesDropdown";
import React from "react";
import { useMutation } from "react-query";
import { useDebounce } from "use-debounce";
import { downloadFiles, useRouter } from "utils";
import { v4 as uuidv4 } from "uuid";

import { ToolGetContext } from "../ToolGetContext";

export const ToolGetRuns: React.FC = () => {
  const { api } = React.useContext(ApiContext);
  const { tool } = React.useContext(ToolGetContext);
  const { workflow } = React.useContext(ReleaseContext);
  const { addNotification, removeNotification } =
    React.useContext(NotificationContext);

  const router = useRouter();

  const [modalState, setModalState] = React.useState<
    "runsCancel" | "runsRetry" | "closed"
  >("closed");

  const [selectedStatuses, setSelectedStatuses] = React.useState<
    Orchestration.RunStatus[] | undefined
  >(undefined);
  const [selectedRecordTitle, setSelectedRecordTitle] = React.useState<
    string | undefined
  >(undefined);
  const [debouncedSelectedRecordTitle] = useDebounce(selectedRecordTitle, 500);
  const [selectedRuns, setSelectedRuns] = React.useState<
    | {
        kind: "uuids";
        uuids: string[];
      }
    | { kind: "all" }
  >({ kind: "uuids", uuids: [] });
  const [selectedNodeSlugs, setSelectedNodeSlugs] = React.useState<
    string[] | undefined
  >(undefined);
  const [selectedRelease, setSelectedRelease] = React.useState<
    Orchestration.Release | undefined
  >(undefined);
  const [selectedGroupNodeUuid, setSelectedGroupNodeUuid] = React.useState<
    string | undefined
  >(undefined);
  const [runsCount, setRunsCount] = React.useState<number>(0);

  const [executionsFilter, setExecutionsFilter] =
    React.useState<Orchestration.ExecutionsFilter>({
      groups: [],
      conjonction: "and",
    });

  const urlState = React.useMemo(() => {
    return {
      selectedStatuses,
      selectedRecordTitle: debouncedSelectedRecordTitle,
      selectedNodeSlugs,
      selectedReleaseUuid:
        selectedRelease !== undefined ? selectedRelease.uuid : undefined,
      selectedGroupNodeUuid,
      executionsFilter,
    };
  }, [
    debouncedSelectedRecordTitle,
    executionsFilter,
    selectedGroupNodeUuid,
    selectedNodeSlugs,
    selectedRelease,
    selectedStatuses,
  ]);

  const getUrlStateSearchParam = React.useCallback(() => {
    return router.searchParams.get("state");
  }, [router]);

  const setUrlStateSearchParam = React.useCallback(
    (stringifiedState: string) => {
      const updatedSearchParams = new URLSearchParams(router.searchParams);

      updatedSearchParams.set("state", stringifiedState);

      router.workspace.updateSearchParams(updatedSearchParams, {
        type: "replace",
      });
    },
    [router],
  );

  const deleteUrlStateSearchParam = React.useCallback(() => {
    const updatedSearchParams = new URLSearchParams(router.searchParams);

    updatedSearchParams.delete("state");

    router.workspace.updateSearchParams(updatedSearchParams, {
      type: "replace",
    });
  }, [router]);

  const { hasRestoredState } = useDeferredUrlState({
    state: urlState,
    getSearchParam: getUrlStateSearchParam,
    setSearchParam: setUrlStateSearchParam,
    deleteSearchParam: deleteUrlStateSearchParam,
    onRestoreState: async (state) => {
      const getSelectedRelease = async () => {
        if (state.selectedReleaseUuid === undefined) {
          return undefined;
        }

        try {
          const response = await api.orchestration.release.get(
            state.selectedReleaseUuid,
          );

          return response.release;
        } catch {
          return undefined;
        }
      };

      const selectedRelease = await getSelectedRelease();

      setSelectedStatuses(state.selectedStatuses);
      setSelectedRecordTitle(state.selectedRecordTitle);
      setSelectedNodeSlugs(state.selectedNodeSlugs);
      setSelectedRelease(selectedRelease);
      setSelectedGroupNodeUuid(state.selectedGroupNodeUuid);
      setExecutionsFilter(state.executionsFilter);
    },
  });

  const runsRetryModalFilter =
    React.useMemo<Orchestration.BatchDataRunsFilter>(() => {
      return {
        kind: "runs",
        uuids: selectedRuns.kind === "uuids" ? selectedRuns.uuids : undefined,
        releaseUuid:
          selectedRelease !== undefined ? selectedRelease.uuid : undefined,
        statuses: selectedStatuses,
        isGroupParent: selectedGroupNodeUuid !== undefined,
        parentNodeUuid: selectedGroupNodeUuid,
      };
    }, [
      selectedGroupNodeUuid,
      selectedRelease,
      selectedRuns,
      selectedStatuses,
    ]);

  const exportingRunsNotificationIdRef = React.useRef<string | undefined>(
    undefined,
  );

  const runsDownloadState = useMutation(api.orchestration.run.download, {
    onSuccess: ({ url }) => {
      downloadFiles([url]);

      if (exportingRunsNotificationIdRef.current !== undefined) {
        removeNotification(exportingRunsNotificationIdRef.current);
      }
    },
    onError: () => {
      addNotification({ type: "error", title: "Failed to download runs." });
    },
    onMutate: () => {
      exportingRunsNotificationIdRef.current = uuidv4();

      addNotification({
        id: exportingRunsNotificationIdRef.current,
        type: "loading",
        title: "Exporting runs.",
        duration: 15 * 1000,
      });
    },
  });

  const workflowGroupNodes = React.useMemo(() => {
    return workflow.nodes.filter(
      (node) => node.kind === "native" && node.actionSlug === "group",
    );
  }, [workflow.nodes]);

  const workflowNodes = React.useMemo<Orchestration.WorkflowNode[]>(() => {
    if (selectedGroupNodeUuid === undefined) {
      return workflow.nodes;
    }

    const workflowGroupNode = workflow.nodes.find(
      (node) => node.uuid === selectedGroupNodeUuid,
    );

    if (workflowGroupNode === undefined) {
      return [];
    }

    return workflowGroupNode.nodes;
  }, [selectedGroupNodeUuid, workflow.nodes]);

  React.useEffect(() => {
    setSelectedNodeSlugs(
      workflowNodes.some((workflowNode) => {
        return workflowNode.isArchived === true;
      })
        ? workflowNodes
            .filter((node) => {
              return node.isArchived === false;
            })
            .map((node) => node.slug)
        : undefined,
    );
  }, [workflowNodes]);

  const runsTableFilters = React.useMemo(() => {
    return {
      groupNodeUuid: selectedGroupNodeUuid,
      release: selectedRelease,
      statuses: selectedStatuses,
      nodeSlugs: selectedNodeSlugs,
      recordTitle: debouncedSelectedRecordTitle,
      executionsFilter,
    };
  }, [
    debouncedSelectedRecordTitle,
    executionsFilter,
    selectedGroupNodeUuid,
    selectedNodeSlugs,
    selectedRelease,
    selectedStatuses,
  ]);

  return (
    <React.Fragment>
      <div className="relative flex h-full w-full flex-col items-start justify-start">
        <div className="flex w-full shrink-0 flex-row items-center justify-start border-b border-neutral-300/50 py-2 dark:border-neutral-700/50">
          <div className="flex h-full shrink-0 flex-row items-center justify-start border-r border-neutral-300/50 px-2 dark:border-neutral-700/50">
            <InputSearch
              value={selectedRecordTitle}
              placeholder="Search..."
              className="!w-[300px]"
              onChange={(event) => {
                setSelectedRecordTitle(event.target.value);
              }}
            />
          </div>
          <div className="flex min-w-0 flex-1 items-center justify-start space-x-2 px-2">
            <div className="shrink-0">
              <WorkflowNodesDropdown
                workflow={workflow}
                workflowNodes={workflowNodes}
                defaultNodeSlugs={selectedNodeSlugs}
                parentNodeUuid={selectedGroupNodeUuid}
                onChange={(nodeSlugs) => {
                  setSelectedNodeSlugs(nodeSlugs);
                }}
              />
            </div>
            <div className="shrink-0">
              <ExecutionsFilterDropdown
                workflow={workflow}
                workflowNodes={workflowNodes}
                filter={executionsFilter}
                parentNodeUuid={selectedGroupNodeUuid}
                onChange={setExecutionsFilter}
              />
            </div>
            <div className="shrink-0">
              <RunStatusesDropdown
                defaultStatuses={selectedStatuses}
                onChange={(statuses) => {
                  setSelectedStatuses(statuses);
                }}
              />
            </div>
            {tool.isReadOnly === false ? (
              <div className="shrink-0">
                <ReleasesDropdown
                  workflow={workflow}
                  defaultRelease={selectedRelease}
                  isDisabled={hasRestoredState === false}
                  onChange={(release) => {
                    setSelectedRelease(release);
                  }}
                />
              </div>
            ) : null}
          </div>
          <div className="shrink-0 px-2">
            {selectedRuns.kind === "uuids" &&
            selectedRuns.uuids.length === 0 ? (
              <span>{runsCount} runs </span>
            ) : null}
            {selectedRuns.kind === "uuids" && selectedRuns.uuids.length > 0 ? (
              <React.Fragment>
                <span>
                  {selectedRuns.uuids.length}/{runsCount} records selected
                </span>{" "}
                (or{" "}
                <Button
                  type="link"
                  text="Select all"
                  onClick={() => {
                    setSelectedRuns({ kind: "all" });
                  }}
                />
                )
              </React.Fragment>
            ) : null}
            {selectedRuns.kind === "all" ? (
              <span>All {runsCount} records selected</span>
            ) : null}
          </div>
          {selectedRuns.kind === "all" || selectedRuns.uuids.length > 0 ? (
            <React.Fragment>
              <div className="flex shrink-0 flex-row items-center justify-start space-x-2 border-l border-neutral-300/50 px-2 dark:border-neutral-700/50">
                <Button
                  className="shrink-0"
                  text="Cancel"
                  icon={<AiOutlineStop />}
                  type="secondary"
                  onClick={() => {
                    setModalState("runsCancel");
                  }}
                />
                <Button
                  className="shrink-0"
                  text="Retry"
                  icon={<IoRefreshOutline />}
                  type="secondary"
                  onClick={() => {
                    setModalState("runsRetry");
                  }}
                />
              </div>
            </React.Fragment>
          ) : null}
        </div>
        <div className="min-h-0 w-full flex-1">
          <RunsTable
            workflow={workflow}
            workflowNodes={workflowNodes}
            selectedRuns={selectedRuns}
            filters={runsTableFilters}
            isLoading={hasRestoredState === false}
            onSelectRuns={setSelectedRuns}
            onRunsFetch={(count) => {
              setRunsCount(count);
            }}
          />
        </div>
        <div className="flex w-full shrink-0 flex-row items-center justify-start border-t border-neutral-300/50 p-2 dark:border-neutral-700/50">
          <div className="min-w-0 flex-1">
            <Tabs
              type="underline"
              className="-mb-2"
              value={
                selectedGroupNodeUuid === undefined
                  ? "default"
                  : `group:${selectedGroupNodeUuid}`
              }
            >
              <TabsItem
                value="default"
                onClick={() => {
                  setSelectedGroupNodeUuid(undefined);
                }}
              >
                <div className="flex flex-row items-center justify-center space-x-2">
                  <span className="shrink-0">
                    <div className="flex h-6 w-6 items-center justify-center rounded-md border border-neutral-300/50 bg-white dark:border-neutral-700/50 dark:bg-neutral-900">
                      <AlignJustify className="h-4 w-4 shrink-0" />
                    </div>
                  </span>
                  <span className="min-w-0 flex-1 truncate text-left font-medium">
                    Default
                  </span>
                </div>
              </TabsItem>
              {workflowGroupNodes.map((workflowGroupNode, index) => (
                <TabsItem
                  key={index}
                  value={`group:${workflowGroupNode.uuid}`}
                  onClick={() => {
                    setSelectedGroupNodeUuid(workflowGroupNode.uuid);
                  }}
                >
                  <div className="flex flex-row items-center justify-center space-x-2">
                    <span className="shrink-0">
                      <WorkflowNodeIcon
                        workflow={workflow}
                        node={workflowGroupNode}
                      />
                    </span>
                    <span className="min-w-0 flex-1 truncate text-left font-medium">
                      {workflowGroupNode.isArchived === true
                        ? "🗑️ Group (Archived)"
                        : "Group"}
                    </span>
                    <LabelBox
                      className="shrink-0"
                      slug={workflowGroupNode.slug}
                    />
                  </div>
                </TabsItem>
              ))}
            </Tabs>
          </div>
          <div className="shrink-0">
            <Dropdown
              title={<IoIosMore />}
              type="secondary"
              hasChevron={false}
              placement="top end"
            >
              <DropdownGroup>
                <DropdownItem
                  icon={<FiDownload />}
                  onClick={() => {
                    runsDownloadState.mutate({
                      workflowUuid: workflow.uuid,
                      releaseUuid:
                        selectedRelease !== undefined
                          ? selectedRelease.uuid
                          : undefined,
                      statuses: selectedStatuses,
                      recordTitle:
                        debouncedSelectedRecordTitle !== undefined
                          ? `%${debouncedSelectedRecordTitle}%`
                          : undefined,
                      isGroupParent: selectedGroupNodeUuid !== undefined,
                      parentNodeUuid: selectedGroupNodeUuid,
                      executionsFilter,
                    });
                  }}
                >
                  <div className="whitespace-nowrap">Export runs</div>
                </DropdownItem>
              </DropdownGroup>
            </Dropdown>
          </div>
        </div>
      </div>
      <RunsCancelModal
        workflowUuid={workflow.uuid}
        releaseUuid={
          selectedRelease !== undefined ? selectedRelease.uuid : undefined
        }
        uuids={selectedRuns.kind === "uuids" ? selectedRuns.uuids : undefined}
        parentNodeUuid={selectedGroupNodeUuid}
        isGroupParent={selectedGroupNodeUuid !== undefined}
        isOpen={modalState === "runsCancel"}
        onSuccess={() => {
          setModalState("closed");
          setSelectedRuns({ kind: "uuids", uuids: [] });
        }}
        onCancel={() => {
          setModalState("closed");
        }}
      />
      <RunsRetryModal
        workflowUuid={workflow.uuid}
        filter={runsRetryModalFilter}
        isOpen={modalState === "runsRetry"}
        onSuccess={() => {
          setModalState("closed");
          setSelectedRuns({ kind: "uuids", uuids: [] });
        }}
        onCancel={() => {
          setModalState("closed");
        }}
      />
    </React.Fragment>
  );
};
