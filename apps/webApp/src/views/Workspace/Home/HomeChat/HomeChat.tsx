import Box from "@untitled-ui/icons-react/build/esm/Box";
import type { Ai } from "cargo-api";
import { Badge } from "cargo-components/Badge";
import { Dropdown } from "cargo-components/Dropdown";
import { ReleaseChat } from "modules/Ai/ReleaseChat";
import { ReleaseContext } from "modules/Ai/ReleaseContext";
import { ToolsEdit } from "modules/Ai/ToolsEdit";
import React from "react";

const SYSTEM_PROMPT = `
You are Cargo's GTM Agent, an AI assistant specialized in Go-To-Market operations. Your goal is to help the user with tasks like lead enrichment, opportunity scoring, campaign planning, content generation, and performance analysis.
`;

type Props = {
  agent: Ai.Agent;
};

export const HomeChat: React.FC<Props> = (props) => {
  const { agent } = props;
  const { draftRelease, updateDraftRelease } = React.useContext(ReleaseContext);

  React.useEffect(() => {
    if (draftRelease.systemPrompt !== SYSTEM_PROMPT) {
      updateDraftRelease({ ...draftRelease, systemPrompt: SYSTEM_PROMPT });
    }
  }, [draftRelease, updateDraftRelease]);

  return (
    <ReleaseChat
      agent={agent}
      release={draftRelease}
      triggerType="draft"
      className="size-full"
      inputToolbar={
        <Dropdown
          hasChevron={false}
          type="secondary"
          title={
            <div className="flex flex-row items-center justify-center space-x-2">
              <Box className="size-3.5" />
              <span>Tools</span>
              {draftRelease.tools.length > 0 ? (
                <Badge className="!rounded-full" color="white">
                  {draftRelease.tools.length}
                </Badge>
              ) : null}
            </div>
          }
        >
          <div className="-m-px w-[300px]">
            <ToolsEdit
              tools={draftRelease.tools}
              onChange={(tools) => {
                updateDraftRelease({ ...draftRelease, tools });
              }}
            />
          </div>
        </Dropdown>
      }
    />
  );
};
