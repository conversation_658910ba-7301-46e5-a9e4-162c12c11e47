{"name": "cargo-web-app", "homepage": "https://app.getcargo.io", "private": true, "engines": {"node": "22.x"}, "volta": {"node": "22.14.0"}, "scripts": {"dev": "next dev -p 3000", "start": "next start", "build": "next build", "build:analyze": "ANALYZE=true next build", "lint": "eslint ./ --fix", "lint:check": "eslint ./ --max-warnings=0", "test": "vitest run", "test:watch": "vitest", "type:check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@auth0/auth0-react": "^2.0.0", "@calcom/embed-react": "^1.3.2", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/lang-python": "^6.2.1", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.3", "@codemirror/view": "^6.37.2", "@datadog/browser-logs": "^5.28.0", "@headlessui/react": "2.1.10", "@hello-pangea/dnd": "18.0.1", "@hotjar/browser": "^1.0.9", "@react-icons/all-files": "https://github.com/react-icons/react-icons/releases/download/v5.0.1/react-icons-all-files-5.0.1.tgz", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@untitled-ui/icons-react": "0.1.4", "axios": "1.8.2", "cargo-api": "*", "cargo-components": "*", "cargo-react-utils": "*", "cargo-utils": "*", "chart.js": "^4.5.0", "classnames": "^2.5.1", "codemirror": "^6.0.2", "diff-match-patch": "^1.0.5", "elkjs": "^0.10.0", "emoji-mart": "^5.6.0", "framer-motion": "^12.9.4", "html-to-image": "^1.11.11", "humanize-duration": "^3.32.1", "jsonschema": "^1.4.1", "moment": "^2.30.1", "next": "14.2.30", "next-themes": "^0.3.0", "orderedmap": "^2.1.1", "papaparse": "^5.5.3", "posthog-js": "^1.161.2", "prism-react-renderer": "^2.4.1", "prosemirror-example-setup": "^1.2.3", "prosemirror-inputrules": "^1.5.0", "prosemirror-markdown": "^1.13.2", "prosemirror-model": "^1.25.1", "prosemirror-schema-basic": "^1.2.4", "prosemirror-schema-list": "^1.5.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.40.0", "react": "18.3.1", "react-data-grid": "7.0.0-beta.47", "react-dom": "18.3.1", "react-error-boundary": "^4.0.13", "react-infinite-scroll-component": "^6.1.0", "react-inlinesvg": "^4.1.3", "react-markdown": "^9.0.1", "react-query": "3.39.3", "react-router": "^6.26.2", "react-use-intercom": "^5.1.4", "reactflow": "^11.11.4", "rehype-raw": "^7.0.0", "semver": "^7.6.3", "sharp": "0.33.5", "snake-case": "^4.0.0", "use-debounce": "^10.0.3", "use-undoable": "^5.0.0", "uuid": "^10.0.0", "zod": "^3.23.8"}, "devDependencies": {"@next/bundle-analyzer": "14.2.30", "@types/chrome": "^0.0.271", "@types/flat": "^5.0.5", "@types/humanize-duration": "^3.27.4", "@types/node": "^20.10.8", "@types/papaparse": "^5.3.16", "@types/react": "^18.3.7", "@types/react-datepicker": "^4.19.3", "@types/react-dom": "^18.3.0", "@types/semver": "^7.5.6", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.16", "cargo-eslint-config": "*", "cargo-prettier-config": "*", "cargo-ts-config": "*", "eslint": "8.57.1", "postcss": "^8.4.31", "prettier": "3.3.3", "sass": "^1.69.5", "tailwindcss": "3.4.1", "typescript": "5.3.2", "vite": "6.3.5", "vitest": "3.1.3"}}