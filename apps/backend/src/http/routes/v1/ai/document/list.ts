import type { Response } from "express";
import { z } from "zod";

import type { AiServices } from "../../../../../domains/ai/index.js";
import type { AuthRequest } from "../../../../middlewares/auth.js";

const querySchema = z.object({
  uuid: z.string(),
  limit: z.string().optional(),
  offset: z.string().optional(),
});

export interface Dependencies {
  aiServices: AiServices;
}

export const buildListController = ({ aiServices }: Dependencies) => {
  const controller = async (req: AuthRequest, res: Response) => {
    const parsedQuery = querySchema.safeParse(req.query);

    if (parsedQuery.success === false) {
      res.status(400).json({ errorMessage: parsedQuery.error });
      return;
    }

    const { uuid, limit = "100", offset = "0" } = parsedQuery.data;

    const parsedLimit = parseInt(limit, 10);
    const parsedOffset = parseInt(offset, 10);

    if (parsedLimit < 1 || parsedLimit > 100) {
      res.status(400).json({ errorMessage: "Limit must be between 1 and 100" });
      return;
    }

    if (parsedOffset < 0) {
      res
        .status(400)
        .json({ errorMessage: "Offset must be bigger or equal to zero" });
      return;
    }

    const documents = await aiServices.document.list({
      workspaceUuid: req.auth!.selectedWorkspaceUuid,
      uuid,
      limit: parsedLimit,
      offset: parsedOffset,
    });

    res.json({ documents });
  };

  return controller;
};
