import type { Response } from "express";
import { z } from "zod";

import type { AiServices } from "../../../../../domains/ai/index.js";
import { AiValidation } from "../../../../../domains/ai/index.js";
import type { AuthRequest } from "../../../../middlewares/auth.js";

const bodySchema = z.object({
  uuid: z.string(),
  kind: AiValidation.documentKind,
  title: z.string(),
  content: z.string(),
});

export interface Dependencies {
  aiServices: AiServices;
}

export const buildCreateController = ({ aiServices }: Dependencies) => {
  const controller = async (req: AuthRequest, res: Response) => {
    const parsedBody = bodySchema.safeParse(req.body);

    if (parsedBody.success === false) {
      res.status(400).json({ errorMessage: parsedBody.error.message });
      return;
    }

    const { uuid, kind, title, content } = parsedBody.data;

    const result = await aiServices.document.create({
      workspaceUuid: req.auth!.selectedWorkspaceUuid,
      userUuid: req.auth!.userUuid,
      uuid,
      kind,
      title,
      content,
    });

    if (result.outcome === "notCreated") {
      res.status(400).json({
        reason: result.reason,
        errorMessage: result.errorMessage,
      });
      return;
    }

    res.status(201).json({ document: result.document });
  };

  return controller;
};
