import type { Response } from "express";
import { z } from "zod";

import { type AiServices } from "../../../../../domains/ai/index.js";
import type { AuthRequest } from "../../../../middlewares/auth.js";

const bodySchema = z.object({
  chatUuid: z.string(),
  text: z.string(),
  uuid: z.string(),
  attachments: z.array(
    z.object({
      name: z.string().optional(),
      s3Filename: z.string(),
      contentType: z.string(),
    }),
  ),
});

export interface Dependencies {
  aiServices: AiServices;
}

export const buildStreamController = ({ aiServices }: Dependencies) => {
  const controller = async (req: AuthRequest, res: Response) => {
    const parsedBody = bodySchema.safeParse(req.body);

    if (parsedBody.success === false) {
      res.status(400).json({ errorMessage: parsedBody.error.message });
      return;
    }

    const { chatUuid, text, uuid, attachments } = parsedBody.data;

    const result = await aiServices.message.stream({
      workspaceUuid: req.auth!.selectedWorkspaceUuid,
      userUuid: req.auth!.userUuid,
      chatUuid,
      parts: [{ type: "text", text }],
      attachments,
      uuid,
    });

    if (result.outcome === "notStreamed") {
      if (result.reason === "chatNotFound") {
        res.status(404).json({ reason: result.reason });
        return;
      }

      if (result.reason === "releaseNotFound") {
        res.status(404).json({ reason: result.reason });
        return;
      }

      res.status(400).json({
        reason: "unknown",
      });
      return;
    }

    result.assistantStream.pipeDataStreamToResponse(res, {
      getErrorMessage: (error) => {
        return error instanceof Error ? error.message : "Unknown error";
      },
      sendReasoning: true,
    });
  };

  return controller;
};
