import type { AiTypes } from "../../index.js";
import type { AiServicesDependencies } from "../dependencies.js";

export type ListPayload = {
  workspaceUuid: string;
  uuid: string;
  limit?: number;
  offset?: number;
};

export const list = async (
  dependencies: AiServicesDependencies,
  payload: ListPayload,
): Promise<AiTypes.Document[]> => {
  const { repositories } = dependencies;
  const { workspaceUuid, uuid, limit, offset } = payload;

  const documents = await repositories.pg.document.list({
    workspaceUuid,
    uuid,
    limit,
    offset,
  });

  return documents;
};
