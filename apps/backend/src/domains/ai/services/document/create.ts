import type { AiTypes } from "../../index.js";
import type { AiServicesDependencies } from "../dependencies.js";

export type CreatePayload = {
  workspaceUuid: string;
  userUuid: string;
  uuid: string;
  kind: AiTypes.DocumentKind;
  title: string;
  content: string;
};

export type CreateResult =
  | {
      outcome: "created";
      document: AiTypes.Document;
    }
  | {
      outcome: "notCreated";
      reason: "invalidContent";
      errorMessage: string;
    };

export const create = async (
  dependencies: AiServicesDependencies,
  payload: CreatePayload,
): Promise<CreateResult> => {
  const { repositories, artifacts } = dependencies;
  const { workspaceUuid, userUuid, uuid, kind, title, content } = payload;

  const documentHandler = artifacts.document[kind];

  if (documentHandler === undefined) {
    return {
      outcome: "notCreated",
      reason: "invalidContent",
      errorMessage: `Invalid document kind: ${kind}`,
    };
  }

  const document = await repositories.pg.document.create({
    uuid,
    workspaceUuid,
    userUuid,
    kind,
    title,
    content,
  });

  return {
    outcome: "created",
    document,
  };
};
