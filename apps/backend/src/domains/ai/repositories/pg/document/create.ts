import type { AiTypes } from "../../../index.js";
import type { AiPgRepositoriesDependencies } from "../dependencies.js";
import { zodDocumentRow } from "./document.js";
import { reshapeDocumentRow } from "./reshape.js";

export type CreatePayload = {
  uuid: string;
  workspaceUuid: string;
  userUuid: string;
  title: string | null;
  content: string;
  kind: AiTypes.DocumentKind;
};

export const create = async (
  dependencies: AiPgRepositoriesDependencies,
  payload: CreatePayload,
): Promise<AiTypes.Document> => {
  const { logger, knex } = dependencies;
  const { uuid, workspaceUuid, userUuid, title, content, kind } = payload;

  try {
    const [row] = await knex("ai.documents")
      .insert({
        uuid,
        workspace_uuid: workspaceUuid,
        user_uuid: userUuid,
        title,
        content,
        kind,
        created_at: new Date(),
      })
      .returning("*");

    const parsedRow = zodDocumentRow.parse(row);

    return reshapeDocumentRow(parsedRow);
  } catch (error) {
    logger.error("Failed to create document", { error });

    throw error;
  }
};
