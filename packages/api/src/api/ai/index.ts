import type { Fetcher } from "../../fetcher/index.js";
import { getErrorWithReasonErrorMessage } from "../../fetcher/index.js";
import { Ai } from "../../store/index.js";

interface AiApiDependencies {
  fetcher: Fetcher;
}

export interface AiApi {
  agent: {
    get: (uuid: string) => Promise<Ai.Api.GetAgentResult>;
    create: (
      payload: Ai.Api.CreateAgentPayload,
    ) => Promise<Ai.Api.CreateAgentResult>;
    all: () => Promise<Ai.Api.AllAgentsResult>;
    remove: (uuid: string) => Promise<void>;
    update: (
      payload: Ai.Api.UpdateAgentPayload,
    ) => Promise<Ai.Api.UpdateAgentResult>;
  };
  release: {
    get: (uuid: string) => Promise<Ai.Api.GetReleaseResult>;
    list: (
      payload: Ai.Api.ListReleasesPayload,
    ) => Promise<Ai.Api.ListReleasesResult>;
    getDraft: (
      payload: Ai.Api.GetDraftReleasePayload,
    ) => Promise<Ai.Api.GetDraftReleaseResult>;
    updateDraft: (
      payload: Ai.Api.UpdateDraftReleasePayload,
    ) => Promise<Ai.Api.UpdateDraftReleaseResult>;
    deployDraft: (
      payload: Ai.Api.DeployDraftReleasePayload,
    ) => Promise<Ai.Api.DeployDraftReleaseResult>;
  };
  chat: {
    get: (uuid: string) => Promise<Ai.Api.GetChatResult>;
    list: (payload: Ai.Api.ListChatsPayload) => Promise<Ai.Api.ListChatsResult>;
    create: (
      payload: Ai.Api.CreateChatPayload,
    ) => Promise<Ai.Api.CreateChatResult>;
    remove: (uuid: string) => Promise<void>;
    update: (
      payload: Ai.Api.UpdateChatPayload,
    ) => Promise<Ai.Api.UpdateChatResult>;
  };
  message: {
    list: (
      payload: Ai.Api.ListMessagesPayload,
    ) => Promise<Ai.Api.ListMessagesResult>;
    remove: (uuid: string) => Promise<void>;
  };
  vote: {
    upsert: (
      payload: Ai.Api.UpsertVotePayload,
    ) => Promise<Ai.Api.UpsertVoteResult>;
    list: (payload: Ai.Api.ListVotesPayload) => Promise<Ai.Api.ListVotesResult>;
  };
  document: {
    get: (uuid: string) => Promise<Ai.Api.GetDocumentResult>;
    list: (
      payload: Ai.Api.ListDocumentsPayload,
    ) => Promise<Ai.Api.ListDocumentsResult>;
    create: (
      payload: Ai.Api.CreateDocumentPayload,
    ) => Promise<Ai.Api.CreateDocumentResult>;
  };
  mcpServer: {
    get: (uuid: string) => Promise<Ai.Api.GetMcpServerResult>;
    create: (
      payload: Ai.Api.CreateMcpServerPayload,
    ) => Promise<Ai.Api.CreateMcpServerResult>;
    all: () => Promise<Ai.Api.AllMcpServersResult>;
    remove: (uuid: string) => Promise<void>;
    update: (
      payload: Ai.Api.UpdateMcpServerPayload,
    ) => Promise<Ai.Api.UpdateMcpServerResult>;
  };
}

export const buildAiApi = ({ fetcher }: AiApiDependencies): AiApi => {
  async function getAgent(uuid: string) {
    const result = await fetcher.fetch<Ai.Api.GetAgentResult>({
      method: "get",
      endpoint: `ai/agents/${uuid}`,
    });

    return result;
  }

  async function createAgent(payload: Ai.Api.CreateAgentPayload) {
    const result = await fetcher.fetch<Ai.Api.CreateAgentResult>({
      method: "post",
      endpoint: "ai/agents",
      payload,
    });

    return result;
  }

  async function allAgents() {
    const result = await fetcher.fetch<Ai.Api.AllAgentsResult>({
      method: "get",
      endpoint: "ai/agents/list",
    });

    return result;
  }

  async function removeAgent(uuid: string) {
    try {
      await fetcher.fetch<void>({
        method: "delete",
        endpoint: `ai/agents/${uuid}`,
      });
    } catch (error) {
      throw getErrorWithReasonErrorMessage(
        error,
        Ai.Api.zodRemoveAgentErrorReason,
        {
          agentNotFound: "Agent not found",
        },
      );
    }
  }

  async function updateAgent(payload: Ai.Api.UpdateAgentPayload) {
    const { uuid, ...other } = payload;

    try {
      const result = await fetcher.fetch<Ai.Api.UpdateAgentResult>({
        method: "put",
        endpoint: `ai/agents/${uuid}`,
        payload: other,
      });

      return result;
    } catch (error) {
      throw getErrorWithReasonErrorMessage(
        error,
        Ai.Api.zodUpdateAgentErrorReason,
        {
          agentNotFound: "Agent not found",
        },
      );
    }
  }

  async function getRelease(uuid: string) {
    const result = await fetcher.fetch<Ai.Api.GetReleaseResult>({
      method: "get",
      endpoint: `ai/releases/${uuid}`,
    });

    return result;
  }

  async function listReleases(payload: Ai.Api.ListReleasesPayload) {
    const result = await fetcher.fetch<Ai.Api.ListReleasesResult>({
      method: "get",
      endpoint: "ai/releases/list",
      params: payload,
    });

    return result;
  }

  async function getDraftRelease(payload: Ai.Api.GetDraftReleasePayload) {
    const result = await fetcher.fetch<Ai.Api.GetDraftReleaseResult>({
      method: "get",
      endpoint: `ai/releases/draft`,
      params: payload,
    });

    return result;
  }

  async function updateDraftRelease(payload: Ai.Api.UpdateDraftReleasePayload) {
    const result = await fetcher.fetch<Ai.Api.UpdateDraftReleaseResult>({
      method: "put",
      endpoint: `ai/releases/draft/update`,
      payload,
    });

    return result;
  }

  async function deployDraftRelease(payload: Ai.Api.DeployDraftReleasePayload) {
    const result = await fetcher.fetch<Ai.Api.DeployDraftReleaseResult>({
      method: "post",
      endpoint: `ai/releases/draft/deploy`,
      payload,
    });

    return result;
  }

  async function getChat(uuid: string) {
    try {
      const result = await fetcher.fetch<Ai.Api.GetChatResult>({
        method: "get",
        endpoint: `ai/chats/${uuid}`,
      });

      return result;
    } catch (error) {
      throw getErrorWithReasonErrorMessage(
        error,
        Ai.Api.zodGetChatErrorReason,
        {
          chatNotFound: "Chat not found",
        },
      );
    }
  }

  async function listChats(payload: Ai.Api.ListChatsPayload) {
    const result = await fetcher.fetch<Ai.Api.ListChatsResult>({
      method: "get",
      endpoint: "ai/chats/list",
      params: payload,
    });

    return result;
  }

  async function createChat(payload: Ai.Api.CreateChatPayload) {
    try {
      const result = await fetcher.fetch<Ai.Api.CreateChatResult>({
        method: "post",
        endpoint: "ai/chats",
        payload,
      });

      return result;
    } catch (error) {
      throw getErrorWithReasonErrorMessage(
        error,
        Ai.Api.zodCreateChatErrorReason,
        {
          agentNotFound: "Agent not found",
          releaseNotFound: "Release not found",
        },
      );
    }
  }

  async function removeChat(uuid: string) {
    try {
      await fetcher.fetch<void>({
        method: "delete",
        endpoint: `ai/chats/${uuid}`,
      });
    } catch (error) {
      throw getErrorWithReasonErrorMessage(
        error,
        Ai.Api.zodRemoveChatErrorReason,
        {
          chatNotFound: "Chat not found",
        },
      );
    }
  }

  async function updateChat(payload: Ai.Api.UpdateChatPayload) {
    try {
      const result = await fetcher.fetch<Ai.Api.UpdateChatResult>({
        method: "put",
        endpoint: `ai/chats/${payload.uuid}`,
        payload,
      });

      return result;
    } catch (error) {
      throw getErrorWithReasonErrorMessage(
        error,
        Ai.Api.zodUpdateChatErrorReason,
        {
          chatNotFound: "Chat not found",
        },
      );
    }
  }

  async function listMessages(payload: Ai.Api.ListMessagesPayload) {
    const result = await fetcher.fetch<Ai.Api.ListMessagesResult>({
      method: "get",
      endpoint: "ai/messages/list",
      params: payload,
    });

    return result;
  }

  async function removeMessage(uuid: string) {
    try {
      await fetcher.fetch({
        method: "delete",
        endpoint: `ai/messages/${uuid}`,
      });
    } catch (error) {
      throw getErrorWithReasonErrorMessage(
        error,
        Ai.Api.zodRemoveMessageErrorReason,
        {
          messageNotFound: "Message not found",
          messageNotUser: "Message is not a user message",
        },
      );
    }
  }

  async function upsertVote(payload: Ai.Api.UpsertVotePayload) {
    const result = await fetcher.fetch<Ai.Api.UpsertVoteResult>({
      method: "put",
      endpoint: "ai/votes",
      payload,
    });

    return result;
  }

  async function listVotes(payload: Ai.Api.ListVotesPayload) {
    const result = await fetcher.fetch<Ai.Api.ListVotesResult>({
      method: "get",
      endpoint: "ai/votes/list",
      params: payload,
    });

    return result;
  }

  async function getDocument(uuid: string) {
    const result = await fetcher.fetch<Ai.Api.GetDocumentResult>({
      method: "get",
      endpoint: `ai/documents/${uuid}`,
    });

    return result;
  }

  async function listDocuments(payload: Ai.Api.ListDocumentsPayload) {
    const result = await fetcher.fetch<Ai.Api.ListDocumentsResult>({
      method: "get",
      endpoint: "ai/documents/list",
      params: payload,
    });

    return result;
  }

  async function createDocument(payload: Ai.Api.CreateDocumentPayload) {
    const result = await fetcher.fetch<Ai.Api.CreateDocumentResult>({
      method: "post",
      endpoint: "ai/documents",
      payload,
    });

    return result;
  }

  async function getMcpServer(uuid: string) {
    const result = await fetcher.fetch<Ai.Api.GetMcpServerResult>({
      method: "get",
      endpoint: `ai/mcpServers/${uuid}`,
    });

    return result;
  }

  async function createMcpServer(payload: Ai.Api.CreateMcpServerPayload) {
    const result = await fetcher.fetch<Ai.Api.CreateMcpServerResult>({
      method: "post",
      endpoint: "ai/mcpServers",
      payload,
    });

    return result;
  }

  async function allMcpServers() {
    const result = await fetcher.fetch<Ai.Api.AllMcpServersResult>({
      method: "get",
      endpoint: "ai/mcpServers/list",
    });

    return result;
  }

  async function removeMcpServer(uuid: string) {
    try {
      await fetcher.fetch<void>({
        method: "delete",
        endpoint: `ai/mcpServers/${uuid}`,
      });
    } catch (error) {
      throw getErrorWithReasonErrorMessage(
        error,
        Ai.Api.zodRemoveMcpServerErrorReason,
        {
          mcpServerNotFound: "MCP server not found",
        },
      );
    }
  }

  async function updateMcpServer(payload: Ai.Api.UpdateMcpServerPayload) {
    const { uuid, ...other } = payload;

    try {
      const result = await fetcher.fetch<Ai.Api.UpdateMcpServerResult>({
        method: "put",
        endpoint: `ai/mcpServers/${uuid}`,
        payload: other,
      });

      return result;
    } catch (error) {
      throw getErrorWithReasonErrorMessage(
        error,
        Ai.Api.zodUpdateMcpServerErrorReason,
        {
          mcpServerNotFound: "MCP server not found",
        },
      );
    }
  }

  return {
    agent: {
      get: getAgent,
      create: createAgent,
      all: allAgents,
      remove: removeAgent,
      update: updateAgent,
    },
    release: {
      get: getRelease,
      list: listReleases,
      getDraft: getDraftRelease,
      updateDraft: updateDraftRelease,
      deployDraft: deployDraftRelease,
    },
    chat: {
      get: getChat,
      list: listChats,
      create: createChat,
      remove: removeChat,
      update: updateChat,
    },
    message: {
      list: listMessages,
      remove: removeMessage,
    },
    vote: {
      upsert: upsertVote,
      list: listVotes,
    },
    document: {
      get: getDocument,
      list: listDocuments,
      create: createDocument,
    },
    mcpServer: {
      get: getMcpServer,
      create: createMcpServer,
      all: allMcpServers,
      remove: removeMcpServer,
      update: updateMcpServer,
    },
  };
};
