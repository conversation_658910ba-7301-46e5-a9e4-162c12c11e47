import classNames from "classnames";
import React from "react";

import { GoBackButton } from "../../GoBackButton/GoBackButton";
import { Title } from "../../Title/Title";

type Props = {
  size?: "middle" | "large";
  className?: string;
  rawTitle?: React.ReactNode;
  title?: React.ReactNode;
  header?: React.ReactNode;
  subHeader?: React.ReactNode;
  footer?: React.ReactNode;
  children?: React.ReactNode;
  showHeader?: boolean;
  onGoBack?: () => void;
};

export const ScreenBody: React.FC<Props> = (props) => {
  const {
    size = "middle",
    title,
    rawTitle,
    header,
    children,
    onGoBack,
    className,
    subHeader,
    showHeader = true,
    footer,
  } = props;

  const renderTitle = () => {
    if (rawTitle) {
      return rawTitle;
    }

    if (onGoBack !== undefined) {
      return (
        <GoBackButton title={title} className="w-full" onGoBack={onGoBack} />
      );
    }

    return (
      <Title level={size === "large" ? 1 : 3} className="w-full truncate">
        {title}
      </Title>
    );
  };

  return (
    <div className="flex h-full w-full flex-col items-start justify-start overflow-hidden">
      <div className="w-full shrink-0">
        {showHeader === true ? (
          <React.Fragment>
            <div
              className={classNames(
                "flex w-full flex-row items-center justify-start space-x-2",
                size === "middle" ? "h-12 p-2" : undefined,
                size === "large" ? "p-7" : undefined,
              )}
            >
              <div className="flex h-8 min-w-0 flex-1 items-center justify-start space-x-2">
                {renderTitle()}
              </div>
              {header !== undefined && header !== null ? (
                <div className="shrink-0">{header}</div>
              ) : null}
            </div>
            {subHeader !== undefined && subHeader !== null ? (
              <div
                className={classNames(
                  "w-full shrink-0 border-t border-neutral-300/50 dark:border-neutral-700/50",
                  size === "middle" ? "px-2" : undefined,
                  size === "large" ? "px-7" : undefined,
                )}
              >
                {subHeader}
              </div>
            ) : null}
          </React.Fragment>
        ) : null}
      </div>
      {children !== undefined && children !== null ? (
        <div
          className={classNames(
            "min-h-0 w-full flex-1 border-t border-neutral-300/50 dark:border-neutral-700/50",
            className,
          )}
        >
          {children}
        </div>
      ) : null}
      {footer !== undefined && footer !== null ? (
        <div
          className={classNames(
            "flex h-12 w-full shrink-0 flex-row items-center justify-start space-x-2 border-t border-neutral-300/50 dark:border-neutral-700/50",
            size === "middle" ? "p-2" : undefined,
            size === "large" ? "px-7 py-2" : undefined,
          )}
        >
          {footer}
        </div>
      ) : null}
    </div>
  );
};
